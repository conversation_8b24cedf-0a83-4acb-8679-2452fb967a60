#!/bin/bash

# <PERSON>hập tên feature
echo "🧱 Chọn loại bạn muốn tạo:"
echo "1) Feature (đầy đủ folders + files)"
echo "2) Model"
echo "3) Params"
echo "4) Usecase"
read -p "👉 Nhậ<PERSON> số tương ứng (1-4): " OPTION


BASE_DIR="lib/src"

case $OPTION in
  1)
    read -p "📦 Tên feature: " FEATURE_NAME
    # Danh sách thư mục cần tạo
    DIRS=(
        "$BASE_DIR/data/datasources/remote"
        "$BASE_DIR/data/models"
        "$BASE_DIR/data/repositories"
        "$BASE_DIR/domain/entities"
        "$BASE_DIR/domain/repositories"
        "$BASE_DIR/domain/usecases"
        "$BASE_DIR/presentation/$FEATURE_NAME/view"
        "$BASE_DIR/presentation/$FEATURE_NAME/widgets"
    )

    # <PERSON><PERSON><PERSON> th<PERSON> mục
    for DIR in "${DIRS[@]}"; do
      mkdir -p "$DIR"
    done

    # Tạo các file mẫu (có thể tùy chỉnh nội dung)
    touch "$BASE_DIR/data/datasources/remote/${FEATURE_NAME}_api_service.dart"
    touch "$BASE_DIR/data/models/${FEATURE_NAME}_model.dart"
    touch "$BASE_DIR/data/repositories/${FEATURE_NAME}_repository_impl.dart"

    touch "$BASE_DIR/domain/entities/${FEATURE_NAME}.dart"
    touch "$BASE_DIR/domain/repositories/${FEATURE_NAME}_repository.dart"
    touch "$BASE_DIR/domain/usecases/get_${FEATURE_NAME}.dart"

    touch "$BASE_DIR/presentation/$FEATURE_NAME/view/${FEATURE_NAME}_page.dart"
    touch "$BASE_DIR/presentation/$FEATURE_NAME/widgets/${FEATURE_NAME}_body.dart"

    echo "✅ Đã tạo cấu trúc clean architecture cho feature: $FEATURE_NAME"
    ;;

  2)
    read -p "📄 Tên model (vd: user): " MODEL
    DIR="$BASE_DIR/data/models"
    FILE="$DIR/${MODEL}_model.dart"
    mkdir -p "$DIR"
    touch "$FILE"
    cat <<EOF > "$FILE"
import 'package:json_annotation/json_annotation.dart';
part '${MODEL}_model.g.dart';
EOF
    echo "✅ Đã tạo model: $FILE"
    DIR="$BASE_DIR/domain/entities"
    FILE="$DIR/${MODEL}.dart"
    mkdir -p "$DIR"
    touch "$FILE"
    echo "✅ Đã tạo entity rỗng: $FILE"
    ;;

  3)
    read -p "📄 Tên params (vd: userParam): " PARAMS
    DIR="$BASE_DIR/core/params"
    FILE="$DIR/${PARAMS}_params.dart"
    mkdir -p "$DIR"
    touch "$FILE"
    cat <<EOF > "$FILE"
import 'package:json_annotation/json_annotation.dart';
part '${PARAMS}_params.g.dart';
EOF
    echo "✅ Đã tạo param: $FILE"
    ;;
  *)
    echo "❌ Lựa chọn không hợp lệ."
    ;;
esac
# Thư mục gốc (ví dụ: lib/features)



