#!/bin/bash

echo "🧱 Chọn loại bạn muốn tạo:"
echo "1) Feature (đầy đủ folders + files)"
echo "2) Model"
echo "3) Entity"
echo "4) Usecase"
read -p "👉 <PERSON><PERSON><PERSON><PERSON> số tương ứng (1-4): " OPTION

case $OPTION in
  1)
    read -p "📦 Tên feature: " FEATURE
    BASE_DIR="lib/features/$FEATURE"

    DIRS=(
      "$BASE_DIR/data/datasources"
      "$BASE_DIR/data/models"
      "$BASE_DIR/data/repositories"
      "$BASE_DIR/domain/entities"
      "$BASE_DIR/domain/repositories"
      "$BASE_DIR/domain/usecases"
      "$BASE_DIR/presentation/pages"
      "$BASE_DIR/presentation/widgets"
    )

    for DIR in "${DIRS[@]}"; do
      mkdir -p "$DIR"
    done

    touch "$BASE_DIR/data/datasources/${FEATURE}_remote_datasource.dart"
    touch "$BASE_DIR/data/models/${FEATURE}_model.dart"
    touch "$BASE_DIR/data/repositories/${FEATURE}_repository_impl.dart"
    touch "$BASE_DIR/domain/entities/${FEATURE}_entity.dart"
    touch "$BASE_DIR/domain/repositories/${FEATURE}_repository.dart"
    touch "$BASE_DIR/domain/usecases/get_${FEATURE}.dart"
    touch "$BASE_DIR/presentation/pages/${FEATURE}_page.dart"
    touch "$BASE_DIR/presentation/widgets/${FEATURE}_widget.dart"

    echo "✅ Đã tạo đầy đủ cấu trúc cho feature: $FEATURE"
    ;;

  2)
    read -p "📦 Tên feature: " FEATURE
    read -p "📄 Tên model (vd: user): " MODEL
    DIR="lib/features/$FEATURE/data/models"
    FILE="$DIR/${MODEL}_model.dart"
    mkdir -p "$DIR"
    touch "$FILE"
    echo "✅ Đã tạo model rỗng: $FILE"
    ;;

  3)
    read -p "📦 Tên feature: " FEATURE
    read -p "📄 Tên entity (vd: user): " ENTITY
    DIR="lib/features/$FEATURE/domain/entities"
    FILE="$DIR/${ENTITY}_entity.dart"
    mkdir -p "$DIR"
    touch "$FILE"
    echo "✅ Đã tạo entity rỗng: $FILE"
    ;;

  4)
    read -p "📦 Tên feature: " FEATURE
    read -p "📄 Tên usecase (vd: get_user): " USECASE
    DIR="lib/features/$FEATURE/domain/usecases"
    FILE="$DIR/${USECASE}.dart"
    mkdir -p "$DIR"
    touch "$FILE"
    echo "✅ Đã tạo usecase rỗng: $FILE"
    ;;

  *)
    echo "❌ Lựa chọn không hợp lệ."
    ;;
esac
