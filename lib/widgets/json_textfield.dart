import 'package:flutter/material.dart';
import 'package:re_editor/re_editor.dart';
import 'package:re_highlight/languages/json.dart';
import 'package:re_highlight/styles/atom-one-light.dart';

class JsonTextfield extends StatefulWidget {
  const JsonTextfield(
      {super.key,
      this.readOnly = false,
      required this.onChange,
      required this.controllerCode});
  final bool readOnly;
  final Function() onChange;
  final CodeLineEditingController controllerCode;

  @override
  State<JsonTextfield> createState() => _JsonTextfieldState();
}

class _JsonTextfieldState extends State<JsonTextfield> {
  @override
  Widget build(BuildContext context) {
    return CodeEditor(
      indicatorBuilder:
          (context, editingController, chunkController, notifier) {
        return Row(
          children: [
            DefaultCodeLineNumber(
              textStyle: TextStyle(color: Colors.white),
              controller: editingController,
              notifier: notifier,
            ),
            DefaultCodeChunkIndicator(
                width: 10,
                controller: chunkController,
                painter: DefaultCodeChunkIndicatorPainter(color: Colors.amber),
                notifier: notifier)
          ],
        );
      },
      readOnly: widget.readOnly,
      onChanged: (val) {
        widget.onChange();
      },
      style: CodeEditorStyle(
        textColor: Colors.white,
        chunkIndicatorColor: Colors.amber,
        codeTheme: CodeHighlightTheme(
            languages: {'json': CodeHighlightThemeMode(mode: langJson)},
            theme: atomOneLightTheme),
      ),
      controller: widget.controllerCode,
    );
  }
}
