import 'package:flutter/material.dart';
import 'package:code_builder/code_builder.dart';
import 'package:testwidget/helpers/class_extension.dart';
import 'package:testwidget/theme/theme.dart';
import 'package:testwidget/theme/text.dart';

enum TypeMethod {
  get,
  post,
  put,
  delete,
  multipart,
}

class ApiMethodSection extends StatefulWidget {
  const ApiMethodSection({
    super.key,
    required this.featuresController,
    required this.controllerPath,
    required this.controllerMethodName,
    required this.typeMethod,
    required this.onMethodChanged,
    required this.onMethodNameChanged,
    required this.jsonParamsContent,
    required this.jsonBodyParamsContent,
    required this.jsonModelClasses,
  });

  final TextEditingController featuresController;
  final TextEditingController controllerPath;
  final TextEditingController controllerMethodName;
  final ValueNotifier<TypeMethod?> typeMethod;
  final Function(TypeMethod, String) onMethodChanged;
  final Function(String) onMethodNameChanged;
  final List<Class> jsonParamsContent;
  final List<Class> jsonBodyParamsContent;
  final List<Class> jsonModelClasses;

  @override
  State<ApiMethodSection> createState() => _ApiMethodSectionState();
}

class _ApiMethodSectionState extends State<ApiMethodSection> {
  List<String> _extractPathParams() {
    final regExp = RegExp(r'\{(.*?)\}');
    final matches = regExp.allMatches(widget.controllerPath.text.trim());
    return matches.map((m) => m.group(1) ?? '').toList();
  }

  Widget _buildMethodButton({
    required TypeMethod method,
    required String label,
    required Color? selectedColor,
    required VoidCallback onPressed,
  }) {
    return ValueListenableBuilder(
      valueListenable: widget.typeMethod,
      builder: (context, selectedMethod, child) {
        final isSelected = selectedMethod == method;
        return ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected 
                ? (selectedColor ?? Theme.of(context).colorScheme.primary)
                : Theme.of(context).colorScheme.surface,
            foregroundColor: isSelected 
                ? Colors.white 
                : Theme.of(context).colorScheme.onSurface,
            elevation: isSelected ? 4 : 1,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.lg,
              vertical: AppSpacing.sm,
            ),
          ),
          onPressed: onPressed,
          child: Text(label),
        );
      },
    );
  }

  Widget _buildMethodButtons() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'HTTP Method',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.md),
            Wrap(
              spacing: AppSpacing.sm,
              runSpacing: AppSpacing.sm,
              children: [
                _buildMethodButton(
                  method: TypeMethod.get,
                  label: 'GET',
                  selectedColor: Colors.green,
                  onPressed: () {
                    widget.typeMethod.value = TypeMethod.get;
                    _handleMethodChange(TypeMethod.get, 'GET');
                  },
                ),
                _buildMethodButton(
                  method: TypeMethod.post,
                  label: 'POST',
                  selectedColor: Colors.blue,
                  onPressed: () {
                    widget.typeMethod.value = TypeMethod.post;
                    _handleMethodChange(TypeMethod.post, 'POST');
                  },
                ),
                _buildMethodButton(
                  method: TypeMethod.put,
                  label: 'PUT',
                  selectedColor: Colors.orange,
                  onPressed: () {
                    widget.typeMethod.value = TypeMethod.put;
                    _handleMethodChange(TypeMethod.put, 'PUT');
                  },
                ),
                _buildMethodButton(
                  method: TypeMethod.delete,
                  label: 'DELETE',
                  selectedColor: Colors.red,
                  onPressed: () {
                    widget.typeMethod.value = TypeMethod.delete;
                    _handleMethodChange(TypeMethod.delete, 'DELETE');
                  },
                ),
              ],
            ),
            SizedBox(height: AppSpacing.md),
            Divider(),
            SizedBox(height: AppSpacing.md),
            _buildMethodButton(
              method: TypeMethod.multipart,
              label: 'POST - Multipart Form',
              selectedColor: Colors.purple,
              onPressed: () {
                widget.typeMethod.value = TypeMethod.multipart;
                _handleMethodChange(TypeMethod.multipart, 'POST');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMethodNameInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Method Name',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.sm),
            TextField(
              controller: widget.controllerMethodName,
              decoration: InputDecoration(
                hintText: 'Enter method name...',
                helperText: 'Leave empty to auto-generate',
              ),
              onChanged: (value) {
                widget.onMethodNameChanged(value);
                _handleMethodNameChange(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPathParamsInfo() {
    final params = _extractPathParams();
    if (params.isEmpty) return SizedBox.shrink();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Path Parameters',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.sm),
            Wrap(
              spacing: AppSpacing.sm,
              runSpacing: AppSpacing.xs,
              children: params.map((param) {
                return Chip(
                  label: Text(param),
                  backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMethodChange(TypeMethod method, String methodName) {
    final params = _extractPathParams();
    final autoMethodName = widget.controllerMethodName.text.isNotEmpty
        ? widget.controllerMethodName.text
        : '${method.name.toLowerCase()}${widget.featuresController.text}';
    
    widget.onMethodChanged(method, autoMethodName);
  }

  void _handleMethodNameChange(String value) {
    final currentMethod = widget.typeMethod.value;
    if (currentMethod != null) {
      final methodName = value.isNotEmpty
          ? value
          : '${currentMethod.name.toLowerCase()}${widget.featuresController.text}';
      
      widget.onMethodChanged(currentMethod, methodName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMethodButtons(),
        SizedBox(height: AppSpacing.md),
        ValueListenableBuilder(
          valueListenable: widget.typeMethod,
          builder: (context, method, child) {
            if (method == null) return SizedBox.shrink();
            
            return Column(
              children: [
                _buildMethodNameInput(),
                SizedBox(height: AppSpacing.md),
                _buildPathParamsInfo(),
              ],
            );
          },
        ),
      ],
    );
  }
}
