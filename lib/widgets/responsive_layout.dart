import 'package:flutter/material.dart';
import 'package:testwidget/theme/theme.dart';

enum ScreenSize {
  mobile,
  tablet,
  desktop,
}

class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  static ScreenSize getScreenSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < AppSpacing.mobileBreakpoint) {
      return ScreenSize.mobile;
    } else if (width < AppSpacing.tabletBreakpoint) {
      return ScreenSize.tablet;
    } else {
      return ScreenSize.desktop;
    }
  }

  static bool isMobile(BuildContext context) {
    return getScreenSize(context) == ScreenSize.mobile;
  }

  static bool isTablet(BuildContext context) {
    return getScreenSize(context) == ScreenSize.tablet;
  }

  static bool isDesktop(BuildContext context) {
    return getScreenSize(context) == ScreenSize.desktop;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < AppSpacing.mobileBreakpoint) {
          return mobile;
        } else if (constraints.maxWidth < AppSpacing.tabletBreakpoint) {
          return tablet ?? desktop;
        } else {
          return desktop;
        }
      },
    );
  }
}

class ResponsiveGrid extends StatelessWidget {
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
  });

  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int columns;
        
        if (constraints.maxWidth < AppSpacing.mobileBreakpoint) {
          columns = mobileColumns;
        } else if (constraints.maxWidth < AppSpacing.tabletBreakpoint) {
          columns = tabletColumns;
        } else {
          columns = desktopColumns;
        }

        return Wrap(
          spacing: spacing,
          runSpacing: runSpacing,
          children: children.map((child) {
            final width = (constraints.maxWidth - (spacing * (columns - 1))) / columns;
            return SizedBox(
              width: width,
              child: child,
            );
          }).toList(),
        );
      },
    );
  }
}

class ResponsivePadding extends StatelessWidget {
  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobile = const EdgeInsets.all(16.0),
    this.tablet = const EdgeInsets.all(24.0),
    this.desktop = const EdgeInsets.all(32.0),
  });

  final Widget child;
  final EdgeInsets mobile;
  final EdgeInsets tablet;
  final EdgeInsets desktop;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        EdgeInsets padding;
        
        if (constraints.maxWidth < AppSpacing.mobileBreakpoint) {
          padding = mobile;
        } else if (constraints.maxWidth < AppSpacing.tabletBreakpoint) {
          padding = tablet;
        } else {
          padding = desktop;
        }

        return Padding(
          padding: padding,
          child: child,
        );
      },
    );
  }
}

class ResponsiveContainer extends StatelessWidget {
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth = 1200.0,
    this.padding,
  });

  final Widget child;
  final double maxWidth;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth),
        padding: padding ?? const EdgeInsets.all(32.0),
        child: child,
      ),
    );
  }
}

class ResponsiveSideBySide extends StatelessWidget {
  const ResponsiveSideBySide({
    super.key,
    required this.left,
    required this.right,
    this.leftFlex = 1,
    this.rightFlex = 1,
    this.spacing = 16.0,
    this.breakpoint,
  });

  final Widget left;
  final Widget right;
  final int leftFlex;
  final int rightFlex;
  final double spacing;
  final double? breakpoint;

  @override
  Widget build(BuildContext context) {
    final effectiveBreakpoint = breakpoint ?? AppSpacing.tabletBreakpoint;
    
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < effectiveBreakpoint) {
          // Stack vertically on smaller screens
          return Column(
            children: [
              left,
              SizedBox(height: spacing),
              right,
            ],
          );
        } else {
          // Side by side on larger screens
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: leftFlex, child: left),
              SizedBox(width: spacing),
              Expanded(flex: rightFlex, child: right),
            ],
          );
        }
      },
    );
  }
}

class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  const ResponsiveAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.elevation,
    this.centerTitle,
  });

  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final double? elevation;
  final bool? centerTitle;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth < AppSpacing.mobileBreakpoint;
        
        return AppBar(
          title: title,
          actions: actions,
          leading: leading,
          backgroundColor: backgroundColor,
          elevation: elevation,
          centerTitle: centerTitle ?? !isMobile,
          titleSpacing: isMobile ? 0 : NavigationToolbar.kMiddleSpacing,
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class ResponsiveScaffold extends StatelessWidget {
  const ResponsiveScaffold({
    super.key,
    this.appBar,
    required this.body,
    this.drawer,
    this.endDrawer,
    this.floatingActionButton,
    this.backgroundColor,
  });

  final PreferredSizeWidget? appBar;
  final Widget body;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget? floatingActionButton;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth < AppSpacing.mobileBreakpoint;
        
        return Scaffold(
          appBar: appBar,
          body: ResponsiveContainer(
            child: body,
          ),
          drawer: isMobile ? drawer : null,
          endDrawer: isMobile ? endDrawer : null,
          floatingActionButton: floatingActionButton,
          backgroundColor: backgroundColor,
        );
      },
    );
  }
}
