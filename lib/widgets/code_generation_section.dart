import 'package:flutter/material.dart';
import 'package:code_builder/code_builder.dart';
import 'package:universal_code_viewer/universal_code_viewer.dart';
import 'package:testwidget/json_model_gen.dart';
import 'package:testwidget/theme/theme.dart';
import 'package:testwidget/theme/text.dart';

class CodeGenerationSection extends StatefulWidget {
  const CodeGenerationSection({
    super.key,
    required this.apiMethod,
    required this.repositoryMethod,
    required this.useCaseClass,
    required this.eventBlocClass,
    required this.onMethodBloc,
    required this.controllerRepository,
    required this.onRepositoryNameChanged,
  });

  final ValueNotifier<Method?> apiMethod;
  final ValueNotifier<Method?> repositoryMethod;
  final ValueNotifier<Class?> useCaseClass;
  final ValueNotifier<Class?> eventBlocClass;
  final ValueNotifier<Method?> onMethodBloc;
  final TextEditingController controllerRepository;
  final Function(String) onRepositoryNameChanged;

  @override
  State<CodeGenerationSection> createState() => _CodeGenerationSectionState();
}

class _CodeGenerationSectionState extends State<CodeGenerationSection> {
  int _selectedTabIndex = 0;

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          _buildTab('API Method', 0),
          _buildTab('Repository', 1),
          _buildTab('Use Case', 2),
          _buildTab('Bloc Event', 3),
          _buildTab('Bloc Method', 4),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: AppSpacing.sm,
            horizontal: AppSpacing.xs,
          ),
          decoration: BoxDecoration(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: AppTextStyles.labelMedium.copyWith(
              color: isSelected 
                  ? Colors.white
                  : Theme.of(context).colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRepositoryNameInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Repository Name',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.sm),
            TextField(
              controller: widget.controllerRepository,
              decoration: InputDecoration(
                hintText: 'Enter repository name...',
                helperText: 'Leave empty to auto-generate',
              ),
              onChanged: widget.onRepositoryNameChanged,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCodeViewer() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getTabTitle(),
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.md),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: _buildCodeContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTabTitle() {
    switch (_selectedTabIndex) {
      case 0:
        return 'API Method Code';
      case 1:
        return 'Repository Method Code';
      case 2:
        return 'Use Case Class Code';
      case 3:
        return 'Bloc Event Class Code';
      case 4:
        return 'Bloc Method Code';
      default:
        return 'Generated Code';
    }
  }

  Widget _buildCodeContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildApiMethodCode();
      case 1:
        return _buildRepositoryMethodCode();
      case 2:
        return _buildUseCaseCode();
      case 3:
        return _buildEventBlocCode();
      case 4:
        return _buildOnMethodBlocCode();
      default:
        return Center(
          child: Text(
            'Select a tab to view generated code',
            style: AppTextStyles.bodyMedium,
          ),
        );
    }
  }

  Widget _buildApiMethodCode() {
    return ValueListenableBuilder(
      valueListenable: widget.apiMethod,
      builder: (context, method, child) {
        if (method == null) {
          return Center(
            child: Text(
              'No API method generated yet',
              style: AppTextStyles.bodyMedium,
            ),
          );
        }

        final code = JsonToModelGenerator.genContentMethod(method)
            .replaceAll('{}', ';');

        return UniversalCodeViewer(
          codeLanguage: 'Dart',
          code: code,
          style: SyntaxHighlighterStyles.vscodeDark,
        );
      },
    );
  }

  Widget _buildRepositoryMethodCode() {
    return ValueListenableBuilder(
      valueListenable: widget.repositoryMethod,
      builder: (context, method, child) {
        if (method == null) {
          return Center(
            child: Text(
              'No repository method generated yet',
              style: AppTextStyles.bodyMedium,
            ),
          );
        }

        final code = JsonToModelGenerator.genContentMethod(method)
            .replaceAll('{}', ';');

        return UniversalCodeViewer(
          codeLanguage: 'Dart',
          code: code,
          style: SyntaxHighlighterStyles.vscodeDark,
        );
      },
    );
  }

  Widget _buildUseCaseCode() {
    return ValueListenableBuilder(
      valueListenable: widget.useCaseClass,
      builder: (context, useCaseClass, child) {
        if (useCaseClass == null) {
          return Center(
            child: Text(
              'No use case generated yet',
              style: AppTextStyles.bodyMedium,
            ),
          );
        }

        final code = JsonToModelGenerator.genContentClass(useCaseClass)
            .replaceAll('{}', ';');

        return UniversalCodeViewer(
          codeLanguage: 'Dart',
          code: code,
          style: SyntaxHighlighterStyles.vscodeDark,
        );
      },
    );
  }

  Widget _buildEventBlocCode() {
    return ValueListenableBuilder(
      valueListenable: widget.eventBlocClass,
      builder: (context, eventClass, child) {
        if (eventClass == null) {
          return Center(
            child: Text(
              'No bloc event generated yet',
              style: AppTextStyles.bodyMedium,
            ),
          );
        }

        final code = JsonToModelGenerator.genContentClass(eventClass)
            .replaceAll('{}', ';');

        return UniversalCodeViewer(
          codeLanguage: 'Dart',
          code: code,
          style: SyntaxHighlighterStyles.vscodeDark,
        );
      },
    );
  }

  Widget _buildOnMethodBlocCode() {
    return ValueListenableBuilder(
      valueListenable: widget.onMethodBloc,
      builder: (context, method, child) {
        if (method == null) {
          return Center(
            child: Text(
              'No bloc method generated yet',
              style: AppTextStyles.bodyMedium,
            ),
          );
        }

        final code = JsonToModelGenerator.genContentMethod(method)
            .replaceAll('{}', ';');

        return UniversalCodeViewer(
          codeLanguage: 'Dart',
          code: code,
          style: SyntaxHighlighterStyles.vscodeDark,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildRepositoryNameInput(),
        SizedBox(height: AppSpacing.md),
        _buildTabBar(),
        SizedBox(height: AppSpacing.md),
        Expanded(
          child: _buildCodeViewer(),
        ),
      ],
    );
  }
}
