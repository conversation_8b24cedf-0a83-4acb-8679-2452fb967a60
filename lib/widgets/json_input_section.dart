import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:code_builder/code_builder.dart';
import 'package:mason/mason.dart';
import 'package:selectable_code_view/sourceCodes/code_view_screen.dart';
import 'package:selectable_code_view/sourceCodes/languages/index.dart';
import 'package:testwidget/helpers/class_extension.dart';
import 'package:testwidget/helpers/validate_json.dart';
import 'package:testwidget/json_model_gen.dart';
import 'package:testwidget/theme/theme.dart';
import 'package:testwidget/theme/text.dart';
import 'package:testwidget/widgets/json_textfield.dart';
import 'package:re_editor/re_editor.dart';

enum TypeKey {
  pascal,
  snake,
}

class JsonInputSection extends StatefulWidget {
  const JsonInputSection({
    super.key,
    required this.featuresController,
    required this.controllerPath,
    required this.controllerBody,
    required this.controllerCode,
    required this.typeKey,
    required this.onJsonParamsChanged,
    required this.onJsonBodyParamsChanged,
    required this.onJsonModelChanged,
    required this.onTypeKeyChanged,
    required this.showOverlay,
    required this.hideOverlay,
  });

  final TextEditingController featuresController;
  final TextEditingController controllerPath;
  final TextEditingController controllerBody;
  final CodeLineEditingController controllerCode;
  final ValueNotifier<TypeKey> typeKey;
  final Function(List<Class>) onJsonParamsChanged;
  final Function(List<Class>) onJsonBodyParamsChanged;
  final Function(List<Class>, List<Class>) onJsonModelChanged;
  final Function(TypeKey) onTypeKeyChanged;
  final Function(Widget, double, [double]) showOverlay;
  final Function() hideOverlay;

  @override
  State<JsonInputSection> createState() => _JsonInputSectionState();
}

class _JsonInputSectionState extends State<JsonInputSection> {
  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  void _generateModel(TypeKey typeKey) {
    final jsonPath = {
      "data": jsonDecode(widget.controllerCode.text.isEmpty 
          ? '{}' 
          : widget.controllerCode.text)
    };
    
    final jsonModelClasses = JsonToModelGenerator.generate(
      initialClassName: widget.featuresController.text.camelCase,
      json: jsonPath,
      isPascal: typeKey == TypeKey.pascal,
    );
    
    final entityClasses = jsonModelClasses
        .map((final e) => e.toEntityClass())
        .toList();
    
    widget.onJsonModelChanged(jsonModelClasses, entityClasses);
  }

  Widget _buildPathInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'API Path',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.sm),
            ValueListenableBuilder(
              valueListenable: widget.featuresController,
              builder: (context, vFeature, child) {
                return TextField(
                  onChanged: (value) {
                    final jsonPath = {"path": value};
                    final jsonParam = JsonToModelGenerator.generateParam(
                      initialClassName: vFeature.text.pascalCase,
                      json: jsonPath,
                    );
                    widget.onJsonParamsChanged(jsonParam);
                  },
                  decoration: InputDecoration(
                    hintText: 'Enter API path...',
                    suffixIcon: IconButton(
                      onPressed: () {},
                      onHover: (val) {
                        if (val) {
                          widget.showOverlay(
                            ValueListenableBuilder(
                              valueListenable: widget.controllerPath,
                              builder: (context, vPath, child) {
                                return SelectableCodeView(
                                  code: JsonToModelGenerator.genContentFrom([]),
                                  language: Language.DART,
                                  languageTheme: LanguageTheme.vscodeDark(),
                                  fontSize: 8.0,
                                  withZoom: true,
                                  withLinesCount: true,
                                  expanded: false,
                                );
                              },
                            ),
                            60,
                          );
                        } else {
                          _debounce = Timer(Duration(milliseconds: 20), () {
                            widget.hideOverlay();
                          });
                        }
                      },
                      icon: Icon(Icons.preview),
                    ),
                  ),
                  controller: widget.controllerPath,
                  style: Theme.of(context).textTheme.bodyMedium,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBodyInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Request Body (JSON)',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.sm),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              child: ValueListenableBuilder(
                valueListenable: widget.featuresController,
                builder: (context, vFeature, child) {
                  return TextField(
                    controller: widget.controllerBody,
                    maxLines: null,
                    expands: true,
                    onChanged: (value) {
                      final jsonPath = {
                        "body": jsonDecode(value.isEmpty 
                            ? '{}' 
                            : JsonValidator.autoFixJson(value))
                      };
                      final jsonBody = JsonToModelGenerator.generateParamBody(
                        initialClassName: vFeature.text.pascalCase,
                        json: jsonPath,
                      );
                      widget.onJsonBodyParamsChanged(jsonBody);
                    },
                    readOnly: vFeature.text.isEmpty,
                    decoration: InputDecoration(
                      hintText: 'Enter JSON body...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(AppSpacing.md),
                      suffixIcon: IconButton(
                        onPressed: () {},
                        onHover: (val) {
                          if (val) {
                            widget.showOverlay(
                              ValueListenableBuilder(
                                valueListenable: widget.controllerBody,
                                builder: (context, vPath, child) {
                                  return SelectableCodeView(
                                    code: JsonToModelGenerator.genContentFrom([]),
                                    language: Language.DART,
                                    languageTheme: LanguageTheme.vscodeDark(),
                                    fontSize: 8.0,
                                    withZoom: true,
                                    withLinesCount: true,
                                    expanded: false,
                                  );
                                },
                              ),
                              230,
                            );
                          } else {
                            _debounce = Timer(Duration(milliseconds: 20), () {
                              widget.hideOverlay();
                            });
                          }
                        },
                        icon: Icon(Icons.preview),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSelection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'JSON Key Format',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: AppSpacing.sm),
            ValueListenableBuilder(
              valueListenable: widget.typeKey,
              builder: (context, vType, child) {
                return Row(
                  children: [
                    Expanded(
                      child: RadioListTile<TypeKey>(
                        title: Text('PascalCase (ExampleKey)'),
                        value: TypeKey.pascal,
                        groupValue: vType,
                        onChanged: (val) {
                          if (val != null) {
                            widget.typeKey.value = val;
                            widget.onTypeKeyChanged(val);
                            _generateModel(val);
                          }
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<TypeKey>(
                        title: Text('snake_case (example_key)'),
                        value: TypeKey.snake,
                        groupValue: vType,
                        onChanged: (val) {
                          if (val != null) {
                            widget.typeKey.value = val;
                            widget.onTypeKeyChanged(val);
                            _generateModel(val);
                          }
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJsonEditor() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'JSON Model',
                  style: AppTextStyles.titleMedium,
                ),
                IconButton(
                  onPressed: () {},
                  onHover: (val) {
                    if (val) {
                      widget.showOverlay(
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SelectableCodeView(
                                code: JsonToModelGenerator.genMapperContent([]),
                                language: Language.DART,
                                languageTheme: LanguageTheme.vscodeDark(),
                                fontSize: 8.0,
                                withZoom: true,
                                withLinesCount: true,
                                expanded: false,
                              ),
                            ],
                          ),
                        ),
                        150,
                        400,
                      );
                    } else {
                      _debounce = Timer(Duration(milliseconds: 20), () {
                        widget.hideOverlay();
                      });
                    }
                  },
                  icon: Icon(Icons.preview),
                ),
              ],
            ),
            SizedBox(height: AppSpacing.sm),
            Container(
              height: 300,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              child: ValueListenableBuilder(
                valueListenable: widget.featuresController,
                builder: (context, vFeature, child) {
                  return ValueListenableBuilder(
                    valueListenable: widget.typeKey,
                    builder: (context, vType, child) {
                      return JsonTextfield(
                        controllerCode: widget.controllerCode,
                        readOnly: vFeature.text.isEmpty,
                        onChange: () {
                          _generateModel(vType);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPathInput(),
        SizedBox(height: AppSpacing.md),
        _buildBodyInput(),
        SizedBox(height: AppSpacing.md),
        _buildTypeSelection(),
        SizedBox(height: AppSpacing.md),
        Expanded(child: _buildJsonEditor()),
      ],
    );
  }
}
