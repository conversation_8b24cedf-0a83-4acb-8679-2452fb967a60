import 'package:collection/collection.dart';

extension JsonExt on Map<String, dynamic> {
  /// Get the json map from the source json where key is 'data' or 'Data' etc
  Map<String, dynamic> get getJsonFromDataKey {
    final dataEntry =
        entries.firstWhereOrNull((final e) => e.key.toLowerCase() == 'data');

    if (dataEntry == null) {
      return {};
    }

    final val = dataEntry.value;

    return val is Map ? val as Map<String, dynamic> : <String, dynamic>{};
  }

  Map<String, dynamic> get getJsonBodyFromDataKey {
    final dataEntry =
        entries.firstWhereOrNull((final e) => e.key.toLowerCase() == 'body');

    if (dataEntry == null) {
      return {};
    }

    final val = dataEntry.value;

    return val is Map ? val as Map<String, dynamic> : <String, dynamic>{};
  }

  Map<String, dynamic> get getParams {
    final dataEntry =
        entries.firstWhereOrNull((final e) => e.key.toLowerCase() == 'path');

    if (dataEntry == null) {
      return {};
    }

    final val = dataEntry.value as String;
    final uri = Uri.parse('https://localhost.com$val');

    return uri.queryParameters;
  }
}
