import 'package:collection/collection.dart';

extension MapEntryExt on MapEntry<String, dynamic> {
  /// Check an entry to see if it has a custom data type value
  bool get isValueCustomDataType {
    final val = value;
    var isCustomType = false;
    if (val is List) {
      final valueList = val;
      isCustomType = MapEntry(key, valueList.firstOrNull).isValueCustomDataType;
    }
    if (val is Map) {
      isCustomType = true;
    }

    return isCustomType;
  }

  Map<String, dynamic> get getMapData {
    final val = value;
    if (val is Map) {
      return val as Map<String, dynamic>;
    }
    if (val is List) {
      return val.firstOrNull as Map<String, dynamic>? ?? <String, dynamic>{};
    }

    return {};
  }
}
