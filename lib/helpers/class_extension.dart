// ignore_for_file: lines_longer_than_80_chars

import 'package:code_builder/code_builder.dart';
import 'package:collection/collection.dart';

extension ClassExt on Class {
  Class toEntityClass() {
    final entityFields = <Field>[];
    for (int i = 0; i < fields.length; i++) {
      final entityField = Field(
        (final p0) => p0
          ..name = fields[i].name
          ..type = Reference(fields[i].type?.symbol?.replaceAll('Model?', '?')),
      );
      entityFields.add(entityField);
    }

    final entityClassName = name.replaceAll('Model', '');

    return Class((final c) => c
          ..name = entityClassName
          ..constructors.addAll(constructors)
          ..fields.addAll(entityFields)
        // ..methods.add(
        //   Method(
        //     (final m) => m
        //       ..name = 'copyWith'
        //       ..returns = refer(entityClassName)
        //       ..optionalParameters.addAll(
        //         entityFields.mapIndexed(
        //           (final index, final p) => Parameter(
        //             (final p) => p
        //               ..name = entityFields[index].name
        //               ..type = refer(
        //                 '${entityFields[index].type?.symbol?.replaceAll('?', '')}?',
        //               )
        //               ..named = true,
        //           ),
        //         ),
        //       )
        //       ..body = Code(
        //         'return $entityClassName('
        //         '${entityFields.map(
        //               (final f) => '${f.name}: ${f.name} ?? this.${f.name},',
        //             ).join()}'
        //         ');',
        //       ),
        //   ),
        // ),
        );
  }
}
