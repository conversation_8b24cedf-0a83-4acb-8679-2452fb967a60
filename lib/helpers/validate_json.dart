import 'dart:convert';

/// JSON Validation Result
class JsonValidationResult {
  final bool isValid;
  final String? errorMessage;
  final List<String> suggestions;
  final String? formattedJson;

  JsonValidationResult({
    required this.isValid,
    this.errorMessage,
    this.suggestions = const [],
    this.formattedJson,
  });

  @override
  String toString() {
    if (isValid) {
      return '✅ JSON hợp lệ!';
    } else {
      return '❌ JSON không hợp lệ: $errorMessage\n${suggestions.join('\n')}';
    }
  }
}

class JsonValidator {
  /// Validate JSON string and return detailed result
  static JsonValidationResult validate(String jsonString) {
    if (jsonString.trim().isEmpty) {
      return JsonValidationResult(
        isValid: false,
        errorMessage: 'JSON không được để trống',
        suggestions: ['Vui lòng nhập nội dung JSON'],
      );
    }

    try {
      // Try to parse JSON
      final parsed = jsonDecode(jsonString);

      // If successful, format the JSON
      final formatted = const JsonEncoder.withIndent('  ').convert(parsed);

      return JsonValidationResult(
        isValid: true,
        formattedJson: formatted,
      );
    } catch (e) {
      // Analyze the error and provide suggestions
      final suggestions = _analyzeJsonError(jsonString, e.toString());

      return JsonValidationResult(
        isValid: false,
        errorMessage: e.toString(),
        suggestions: suggestions,
      );
    }
  }

  /// Format JSON string (if valid)
  static String? formatJson(String jsonString) {
    try {
      final parsed = jsonDecode(jsonString);
      return const JsonEncoder.withIndent('  ').convert(parsed);
    } catch (e) {
      return null;
    }
  }

  /// Minify JSON string (if valid)
  static String? minifyJson(String jsonString) {
    try {
      final parsed = jsonDecode(jsonString);
      return jsonEncode(parsed);
    } catch (e) {
      return null;
    }
  }

  /// Check if JSON is valid
  static bool isValidJson(String jsonString) {
    try {
      jsonDecode(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Analyze JSON errors and provide specific suggestions
  static List<String> _analyzeJsonError(
      String jsonString, String errorMessage) {
    List<String> suggestions = [];

    // Check for single quotes
    if (jsonString.contains("'")) {
      suggestions.add(
          '🔍 PHÁT HIỆN: Sử dụng dấu nháy đơn (\') thay vì dấu nháy kép (")');
      suggestions.add('💡 GIẢI PHÁP: Thay tất cả dấu \' bằng dấu "');
    }

    // Check for trailing commas
    if (jsonString.contains(',}') || jsonString.contains(',]')) {
      suggestions.add('🔍 PHÁT HIỆN: Dấu phẩy thừa trước }, ]');
      suggestions
          .add('💡 GIẢI PHÁP: Xóa dấu phẩy cuối cùng trong object/array');
    }

    // Check for unquoted keys
    final unquotedKeyRegex = RegExp(r'\s*[a-zA-Z_][a-zA-Z0-9_]*\s*:');
    final matches = unquotedKeyRegex.allMatches(jsonString);
    for (final match in matches) {
      final key = match.group(0)?.trim().replaceAll(':', '') ?? '';
      if (!key.startsWith('"') && !key.startsWith("'")) {
        suggestions.add('🔍 PHÁT HIỆN: Key "$key" không có dấu nháy');
        suggestions.add('💡 GIẢI PHÁP: Thay "$key:" thành "$key":');
      }
    }

    // Check for undefined/function values
    if (jsonString.contains('undefined') || jsonString.contains('function')) {
      suggestions.add(
          '🔍 PHÁT HIỆN: Giá trị undefined hoặc function không hợp lệ trong JSON');
      suggestions.add('💡 GIẢI PHÁP: Sử dụng null, string hoặc xóa property');
    }

    // Check for comments
    if (jsonString.contains('//') || jsonString.contains('/*')) {
      suggestions.add('🔍 PHÁT HIỆN: JSON không hỗ trợ comment');
      suggestions.add('💡 GIẢI PHÁP: Xóa tất cả comment // hoặc /* */');
    }

    // Add general JSON rules if no specific error found
    if (suggestions.isEmpty) {
      suggestions.addAll([
        '📋 CÁC QUY TẮC JSON CHUẨN:',
        '• Key phải được bao bọc bởi dấu nháy kép ""',
        '• String phải dùng dấu nháy kép "", không dùng dấu nháy đơn \'\'',
        '• Không được có dấu phẩy thừa cuối object {} hoặc array []',
        '• Chỉ sử dụng: string, number, boolean, null, object, array',
        '• Không sử dụng: undefined, function, comment',
      ]);
    }

    return suggestions;
  }

  /// Fix common JSON errors automatically
  static String autoFixJson(String jsonString) {
    String fixed = jsonString;

    // Fix single quotes to double quotes (simple cases)
    fixed = fixed.replaceAllMapped(
      RegExp(r"'([^']*)'"),
      (match) => '"${match.group(1)}"',
    );

    // Remove trailing commas - Fix the regex replacement
    fixed = fixed.replaceAllMapped(
      RegExp(r',(\s*[}\]])'),
      (match) => '${match.group(1)}',
    );

    // Fix unquoted keys (simple cases)
    fixed = fixed.replaceAllMapped(
      RegExp(r'(\s*)([a-zA-Z_][a-zA-Z0-9_]*)(\s*):'),
      (match) => '${match.group(1)}"${match.group(2)}"${match.group(3)}:',
    );

    // Remove comments
    fixed = fixed.replaceAll(RegExp(r'//.*$', multiLine: true), '');
    fixed = fixed.replaceAll(RegExp(r'/\*.*?\*/', dotAll: true), '');

    return fixed;
  }
}
