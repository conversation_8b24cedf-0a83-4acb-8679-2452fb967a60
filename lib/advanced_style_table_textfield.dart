// Flutter imports:
import 'package:flutter/material.dart';

class AdvancedStyleableTextFieldController extends TextEditingController {
  AdvancedStyleableTextFieldController({
    super.text,
    this.curlyBraceColors = const [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ],
    this.squareBracketColors = const [
      Colors.cyan,
      Colors.pink,
      Colors.amber,
      Colors.indigo,
      Colors.lime,
      Colors.brown,
    ],
    this.parenthesesColors = const [
      Colors.deepOrange,
      Colors.lightGreen,
      Colors.deepPurple,
      Colors.yellow,
      Colors.grey,
      Colors.blueGrey,
    ],
    this.enableBold = false,
    this.enableUnderline = false,
  });

  final List<Color> curlyBraceColors;
  final List<Color> squareBracketColors;
  final List<Color> parenthesesColors;
  final bool enableBold;
  final bool enableUnderline;

  @override
  TextSpan buildTextSpan({
    required final BuildContext context,
    final TextStyle? style,
    required final bool withComposing,
  }) {
    final List<InlineSpan> textSpanChildren = <InlineSpan>[];

    // <PERSON> dõi độ sâu của các lo<PERSON>i ngoặc
    int curlyBraceDepth = 0;
    int squareBracketDepth = 0;
    int parenthesesDepth = 0;

    // Duyệt qua từng ký tự
    for (int i = 0; i < text.length; i++) {
      final String char = text[i];
      TextStyle? currentStyle = style;

      switch (char) {
        case '{':
          currentStyle = _getBracketStyle(
            style,
            curlyBraceColors,
            curlyBraceDepth,
          );
          curlyBraceDepth++;
          break;

        case '}':
          curlyBraceDepth =
              (curlyBraceDepth - 1).clamp(0, double.infinity).toInt();
          currentStyle = _getBracketStyle(
            style,
            curlyBraceColors,
            curlyBraceDepth,
          );
          break;

        case '[':
          currentStyle = _getBracketStyle(
            style,
            squareBracketColors,
            squareBracketDepth,
          );
          squareBracketDepth++;
          break;

        case ']':
          squareBracketDepth =
              (squareBracketDepth - 1).clamp(0, double.infinity).toInt();
          currentStyle = _getBracketStyle(
            style,
            squareBracketColors,
            squareBracketDepth,
          );
          break;

        case '(':
          currentStyle = _getBracketStyle(
            style,
            parenthesesColors,
            parenthesesDepth,
          );
          parenthesesDepth++;
          break;

        case ')':
          parenthesesDepth =
              (parenthesesDepth - 1).clamp(0, double.infinity).toInt();
          currentStyle = _getBracketStyle(
            style,
            parenthesesColors,
            parenthesesDepth,
          );
          break;
      }

      // Thêm ký tự vào TextSpan
      textSpanChildren.add(
        TextSpan(
          text: char,
          style: currentStyle,
        ),
      );
    }

    return TextSpan(style: style, children: textSpanChildren);
  }

  TextStyle _getBracketStyle(
    TextStyle? baseStyle,
    List<Color> colors,
    int depth,
  ) {
    final Color color = colors[depth % colors.length];

    return (baseStyle ?? const TextStyle()).copyWith(
      color: color,
      fontWeight: enableBold ? FontWeight.bold : null,
      decoration: enableUnderline ? TextDecoration.underline : null,
    );
  }
}

class TextPartStyleDefinition {
  TextPartStyleDefinition({
    required this.pattern,
    required this.style,
  });
  final String pattern;
  final TextStyle style;
}

class TextPartStyleDefinitions {
  TextPartStyleDefinitions({required this.definitionList});

  final List<TextPartStyleDefinition> definitionList;

  RegExp createCombinedPatternBasedOnStyleMap() {
    final String combinedPatternString = definitionList
        .map<String>(
          (final TextPartStyleDefinition textPartStyleDefinition) =>
              textPartStyleDefinition.pattern,
        )
        .join('|');

    return RegExp(
      combinedPatternString,
      multiLine: true,
      caseSensitive: false,
    );
  }

  TextPartStyleDefinition? getStyleOfTextPart(
    final String textPart,
    final String text,
  ) {
    return List<TextPartStyleDefinition?>.from(definitionList).firstWhere(
      (final TextPartStyleDefinition? styleDefinition) {
        if (styleDefinition == null) {
          return false;
        }

        bool hasMatch = false;

        RegExp(styleDefinition.pattern, caseSensitive: false)
            .allMatches(text)
            .forEach(
          (final RegExpMatch currentMatch) {
            if (hasMatch) {
              return;
            }

            if (currentMatch.group(0) == textPart) {
              hasMatch = true;
            }
          },
        );

        return hasMatch;
      },
      orElse: () => null,
    );
  }
}
