
import 'dart:async';
import 'dart:convert';

import 'package:code_builder/code_builder.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

import 'package:interactive_json_preview/interactive_json_preview.dart';
import 'package:mason/mason.dart';
import 'package:re_editor/re_editor.dart';
import 'package:re_highlight/languages/json.dart';
import 'package:re_highlight/styles/atom-one-light.dart';
import 'package:selectable_code_view/sourceCodes/code_view_screen.dart';
import 'package:selectable_code_view/sourceCodes/languages/index.dart';
import 'package:testwidget/helpers/class_extension.dart';
import 'package:testwidget/helpers/validate_json.dart';
import 'package:testwidget/json_model_gen.dart';
import 'package:testwidget/style_table_textfield.dart';
import 'package:testwidget/widgets/json_textfield.dart';
import 'package:universal_code_viewer/universal_code_viewer.dart';
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

enum TypeKey{
  pascal,
  snake,
}
enum TypeMethod{
  get,
  post,
  put,
  delete,
  multipart
}

// class _CodeProperties{
//   final String method;
//   final String methodName;
// }

class _MyHomePageState extends State<MyHomePage> {
  final _controllerCode = CodeLineEditingController(options: CodeLineOptions(),
  spanBuilder: ({required codeLine, required context, required index, required style, required textSpan}) {
  final List<InlineSpan> textSpanChildren = <InlineSpan>[];
  String text = codeLine.text;
  
  if (text.isEmpty) {
    return TextSpan(style: style, text: text);
  }
  
  // Màu sắc
  final Map<String, Color> colors = {
    'key': Colors.blue.shade700,
    'string': Colors.green.shade600,
    'number': Colors.orange.shade700,
    'boolean': Colors.purple.shade600,
    'null': Colors.grey.shade600,
    'bracket': Colors.red.shade600,
    'punctuation': Colors.grey.shade700,
  };
  
  // Tokenizer đơn giản
  List<Map<String, dynamic>> tokens = [];
  
  for (int i = 0; i < text.length; i++) {
    String char = text[i];
    
    if (char == '"') {
      // Parse string
      int start = i;
      i++; // Skip opening quote
      while (i < text.length && text[i] != '"') {
        if (text[i] == '\\') i++; // Skip escaped character
        i++;
      }
      if (i < text.length) i++; // Skip closing quote
      
      String stringValue = text.substring(start, i);
      
      // Check if it's a key (followed by ':')
      bool isKey = false;
      int nextNonSpace = i;
      while (nextNonSpace < text.length && text[nextNonSpace].trim().isEmpty) {
        nextNonSpace++;
      }
      if (nextNonSpace < text.length && text[nextNonSpace] == ':') {
        isKey = true;
      }
      
      tokens.add({
        'type': isKey ? 'key' : 'string',
        'value': stringValue,
        'start': start,
        'end': i,
      });
      i--; // Adjust for loop increment
      
    } else if (RegExp(r'\d').hasMatch(char) || char == '-') {
      // Parse number
      int start = i;
      if (char == '-') i++;
      while (i < text.length && RegExp(r'[\d.]').hasMatch(text[i])) {
        i++;
      }
      
      tokens.add({
        'type': 'number',
        'value': text.substring(start, i),
        'start': start,
        'end': i,
      });
      i--; // Adjust for loop increment
      
    } else if (char == 't' && text.substring(i).startsWith('true')) {
      tokens.add({
        'type': 'boolean',
        'value': 'true',
        'start': i,
        'end': i + 4,
      });
      i += 3; // Skip 'rue'
      
    } else if (char == 'f' && text.substring(i).startsWith('false')) {
      tokens.add({
        'type': 'boolean',
        'value': 'false',
        'start': i,
        'end': i + 5,
      });
      i += 4; // Skip 'alse'
      
    } else if (char == 'n' && text.substring(i).startsWith('null')) {
      tokens.add({
        'type': 'null',
        'value': 'null',
        'start': i,
        'end': i + 4,
      });
      i += 3; // Skip 'ull'
      
    } else if (['{', '}', '[', ']'].contains(char)) {
      tokens.add({
        'type': 'bracket',
        'value': char,
        'start': i,
        'end': i + 1,
      });
      
    } else if ([':', ','].contains(char)) {
      tokens.add({
        'type': 'punctuation',
        'value': char,
        'start': i,
        'end': i + 1,
      });
    }
  }
  
  // Render tokens
  int currentPos = 0;
  
  for (var token in tokens) {
    // Add any unprocessed characters before this token
    if (token['start'] > currentPos) {
      textSpanChildren.add(
        TextSpan(
          text: text.substring(currentPos, token['start']),
          style: style,
        ),
      );
    }
    
    // Add the token with appropriate color
    Color tokenColor = colors[token['type']] ?? style.color ?? Colors.black;
    textSpanChildren.add(
      TextSpan(
        text: token['value'],
        style: style.copyWith(color: tokenColor),
      ),
    );
    
    currentPos = token['end'];
  }
  
  // Add any remaining characters
  if (currentPos < text.length) {
    textSpanChildren.add(
      TextSpan(
        text: text.substring(currentPos),
        style: style,
      ),
    );
  }
  
  return TextSpan(style: style, children: textSpanChildren);
  },
  );
  List<Class> jsonParamsContent = [];
  List<Class> jsonBodyParamsContent = [];
  List<Class> jsonModelClasses = [];
  List<Class> entityClasses = [];
  late Method apiMethod;
  final ValueNotifier<Class?> vEventBloc = ValueNotifier(null);
  final ValueNotifier<Method?> vOnMethodBloc = ValueNotifier(null);
  final ValueNotifier<Method?> valueCode = ValueNotifier(null);
  final ValueNotifier<Method?> valueCodeRepo = ValueNotifier(null);
  final ValueNotifier<Class?> valueCodeUsecase = ValueNotifier(null);
  late TextEditingController _featuresController;
  late TextEditingController _controller;
  late TextEditingController _controllerPath;
  late TextEditingController _controllerBody;
  late TextEditingController _controllerRepository;
  late TextEditingController _controllerMethodName;
  final ValueNotifier<TypeKey> _typeKey = ValueNotifier(TypeKey.pascal) ;
  final ValueNotifier<TypeMethod?> _typeMethod = ValueNotifier(null) ;
  String methodName = '';
  String methodRepositoryName = '';
  @override
  void initState() {
    _controllerMethodName = TextEditingController();
    _featuresController = TextEditingController();
    _controllerPath = TextEditingController();
    _controllerBody = TextEditingController();
    _controllerRepository = TextEditingController();
    _controller = StyleableTextFieldController(
      text: '',
      styles: TextPartStyleDefinitions(
        definitionList: <TextPartStyleDefinition>[
          TextPartStyleDefinition(
            style: TextStyle(
              color: Colors.green,
             
            ),
            pattern: r'[{}]',
          ),
        ],
      ),
    );
    
  // jsonModelClasses = JsonToModelGenerator.generate(
  //   initialClassName: 'testmodel'.pascalCase,
  //   json: json
  // );
  //   entityClasses =
  //     jsonModelClasses.map((final e) => e.toEntityClass()).toList();
    super.initState();
  }
  OverlayEntry? _overlayEntry;
  bool isShow = false;
  Timer? _debounce;
  bool _isOverlayVisible = false;
    void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isOverlayVisible = false;
    });
  }
  void _showOverlay(
    final Widget body,
    final double paddingTop,
    [final double height = 200]
  ) {
       final renderBox = context.findRenderObject() as RenderBox?;
       final offset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;

    _overlayEntry = OverlayEntry(
       
      builder: (final context) => 
      Positioned(
         left: offset.dx+ 400,
           top: offset.dy +paddingTop, 
        child: Material(
          child: MouseRegion(
            onEnter: (event) {
                   if (_debounce?.isActive ?? false) {
                           _debounce?.cancel();
                  }
            },
            onExit: (event){
              _hideOverlay();
            },
         
            child: SizedBox(
          
            height: height,
            child: body,
            ),
          ),
        ),
      )
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOverlayVisible = true;
    });
  }
  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: TextField(
          controller: _featuresController,
          style: TextStyle(color: Colors.white),
          decoration: 
          InputDecoration(
            filled: true,
            fillColor: Colors.black,
            hintText: "Features...."),
        ),
      ),
      body: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 12,
            children: [
               SizedBox(
                width: 400,
                child:   ValueListenableBuilder(
                            valueListenable: _featuresController,
                            builder: (context, vFeature, child) {
                              return TextField(
                      onChanged: (value) {
                           final jsonPath = {"path": value, };
                         final jsonParam = JsonToModelGenerator.generateParam(
                        initialClassName: vFeature.text.pascalCase,
                        json: jsonPath
                      );
                      jsonParamsContent = jsonParam;
                      },
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.black,
                        suffixIcon: IconButton(
                          onPressed: (){},
                          onHover: (val){
                           
                            if (val) {
                             _showOverlay( 
                              ValueListenableBuilder(
                                valueListenable: _controllerPath,
                                builder: (context, vPath, child) {
                                  return SelectableCodeView(
                                    code: JsonToModelGenerator.genContentFrom(jsonParamsContent,), // Code text
                                          language: Language.DART, // Language
                                            languageTheme: LanguageTheme.vscodeDark(), 
                                    fontSize: 8.0, // Font size
                                    withZoom: true, // Enable/Disable zoom icon controls
                                    withLinesCount: true, // Enable/Disable line number
                                    expanded: false, // Enable/Disable container expansion
                                            );
                                }
                              ),
                            60,  
                            );
                            } else{
                      _debounce=  Timer( Duration(milliseconds: 20), () {
                                _hideOverlay();
                          });
                              
                            }
                         
                        }, icon: Icon(Icons.preview))
                      ),
                      controller: _controllerPath,
                      maxLines: 1,
                      style: TextStyle(color: Colors.white, fontSize: 16),
                      );
                  }
                )
                  ),
               Container(
                color: Colors.black,
                width: 400,
                height: 200,
                child:  ValueListenableBuilder(
                            valueListenable: _featuresController,
                            builder: (context, vFeature, child) {
                              return  TextField(
                      controller: _controllerBody,
                      maxLines: 10,
                      onChanged: (value) {
                     final jsonPath = {"body": jsonDecode(value.isEmpty?'{}'
                                  :JsonValidator.autoFixJson(value)) };
                      final jsonBody = JsonToModelGenerator.generateParamBody(
                        initialClassName: vFeature.text.pascalCase,
                        json: jsonPath
                      );
                      jsonBodyParamsContent = jsonBody;
                      },
                      style: TextStyle(color: Colors.white),
                      readOnly: vFeature.text.isEmpty,
                      decoration: InputDecoration(
                        filled: vFeature.text.isEmpty,
                            fillColor: Colors.grey,
                        suffixIcon: IconButton(
                          onPressed: (){},
                          onHover: (val){
                            
                            if (val) {
                             _showOverlay( 
                              ValueListenableBuilder(
                                valueListenable: _controllerBody,
                                builder: (context, vPath, child) {
                         
                    
                                  return   SelectableCodeView(
                                    code: JsonToModelGenerator.genContentFrom(jsonBodyParamsContent,), 
                                           language: Language.DART, // Language
                                        languageTheme: LanguageTheme.vscodeDark(), 
                                    fontSize: 8.0, // Font size
                                    withZoom: true, // Enable/Disable zoom icon controls
                                    withLinesCount: true, // Enable/Disable line number
                                    expanded: false, // Enable/Disable container expansion
                                            );
                                }
                              ),
                              230
                              );
                            } else{
                      _debounce=  Timer( Duration(milliseconds: 20), () {
                                _hideOverlay();
                          });
                              
                            }
                         
                        }, icon: Icon(Icons.preview))
                      ),
                      );
                  }
                )
                  ),
              ValueListenableBuilder(
                                                  valueListenable: _typeKey,
                                                  builder: (context, vType, child) {
                                                    return Row(
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      children: [
                                                          Row(
                                                            children: [
                                                              Checkbox(value: vType == TypeKey.pascal,
                                                               onChanged: (val){
                                                                _typeKey.value = TypeKey.pascal;
                                                                 _genModel(TypeKey.pascal);
                                                               },
                                                                                                                 
                                                               ),
                                                               Text('Json(name: ExampleKey)'),
                                                            ],
                                                          ),
                                                            Row(
                                                              children: [
                                                                Checkbox(value: vType == TypeKey.snake,
                                                                 onChanged: (val){
                                                                _typeKey.value = TypeKey.snake;
                                                                _genModel(TypeKey.snake);
                                                                     },),
                                                                Text('Json(name: example_key)'),
                                                              ],
                                                            )
                                                      ],
                                                    );
                                                  }
                                                ),
              Expanded(
                child: ValueListenableBuilder(
                      valueListenable: _featuresController,
                      builder: (context, vFeature, child) {
                        return  ValueListenableBuilder(
                      valueListenable: _typeKey,
                      builder: (context, vType, child) {
                        return Stack(
                        
                          children: [
                            Container(
                              color: vFeature.text.isEmpty 
                              ?Colors.grey.withValues(alpha: .5):
                              Colors.black,
                              width: 400,
                              child: _buildTextField(vFeature, vType)
                              
                               
                                ),
                                Positioned(
                                top: 10,
                                 right: 10,
                                  child: IconButton(
                                    onPressed: (){},
                                    onHover: (val){
                                      
                                      if (val) {
                                       _showOverlay( 
                                        SingleChildScrollView(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                                    SizedBox(
                                                      height: 40,
                                                      child: SelectableCodeView(
                                                                         code: JsonToModelGenerator.genMapperContent(jsonModelClasses), // Code text
                                                                                               language: Language.DART, // Language
                                                                                            languageTheme: LanguageTheme.vscodeDark(), // Theme
                                                          fontSize: 8.0, // Font size
                                                          withZoom: true, // Enable/Disable zoom icon controls
                                                          withLinesCount: true, // Enable/Disable line number
                                                          expanded: false, // Enable/Disable container expansion
                                                                  ),
                                                    ),
                                              Row(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Column(
                                                    children: [
                                                    
                                                      SelectableCodeView(
                                                            code: JsonToModelGenerator.genContentFrom(jsonModelClasses), // Code text
                                                                 language: Language.DART, // Language
                                                            languageTheme: LanguageTheme.vscodeDark(), // Theme
                                                            fontSize: 8.0, // Font size
                                                            withZoom: true, // Enable/Disable zoom icon controls
                                                            withLinesCount: true, // Enable/Disable line number
                                                            expanded: false, // Enable/Disable container expansion
                                                                    ),
                                                    ],
                                                  ),
                                                SelectableCodeView(
                                                 code: JsonToModelGenerator.genContentFrom(entityClasses), // Code text
                                              language: Language.DART, // Language
                                                    languageTheme: LanguageTheme.vscodeDark(),  
                                                                          fontSize: 8.0,
                                                                          withZoom: true,
                                                                          withLinesCount: true, 
                                                                          expanded: false,
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        150,
                                        400
                                        );
                                      } else{
                                                                _debounce=  Timer( Duration(milliseconds: 20), () {
                                          _hideOverlay();
                                    });
                                        
                                      }
                                   
                                  }, icon: Icon(Icons.preview)),
                                )
                              
                          ],
                        );
                      }
                    );
                  }
                ),
              ),
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                   SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                     child: Row(
                       children: [
                         ValueListenableBuilder(
                          valueListenable: _typeMethod,
                           builder: (context, vTypeMethod, child) {
                                   final regExp = RegExp(r'\{(.*?)\}');
                               final matches = regExp.allMatches(_controllerPath.text.trim());
                             
                               final params = matches.map((m) => m.group(1)??'').toList();
                             return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                               children: [
                                  Row(
                                    children: [
                                       ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: vTypeMethod == TypeMethod.get? Colors.greenAccent
                                          : Colors.white
                                        ),
                                        onPressed: (){
                                          _typeMethod.value =TypeMethod.get;
                                      methodName = 'GET';
                                      }, child: Text('GET')),
                                      ElevatedButton(onPressed: (){
                                        _typeMethod.value =TypeMethod.post;
                                      
                                      methodName = 'POST';
                                      }, child: Text('POST')),
                                        ElevatedButton(onPressed: (){
                                          _typeMethod.value =TypeMethod.put;
                                         
                                      }, child: Text('PUT')),
                                        ElevatedButton(
                                           style: ElevatedButton.styleFrom(
                                          backgroundColor: vTypeMethod == TypeMethod.delete? Colors.red
                                          : Colors.white
                                        ),
                                          onPressed: (){
                                          _typeMethod.value =TypeMethod.delete;
                                      
                                      }, child: Text('DELETE')),
                                      Row(spacing: 8, children: [
                                        ColoredBox(color: Colors.grey.withValues(alpha: 2),
                                          child: SizedBox(width: 3,
                                          height: 24,),
                                        ),
                                         ElevatedButton(onPressed: (){
                                          _typeMethod.value =TypeMethod.multipart;
                                     
                                      }, child: Text('POST - Multipart-form')),
                             
                                      ],)
                                   
                                    ],
                                  ),
                                  ValueListenableBuilder(
                                    valueListenable: _typeMethod,
                                    builder: (context, vMethod, child) {
                                      if(vMethod != null){
                                           final methodName = '${vMethod.name.toLowerCase()}'
                                      '${_featuresController.text}';
                                     final method =  apiMethod45(vMethod.name,
                                           segments: params,
                                           nameMethod: methodName,
                                           paramName: jsonParamsContent.firstOrNull?.name,
                                           bodyName: jsonBodyParamsContent.firstOrNull?.name,
                                           modelName: jsonModelClasses.firstOrNull?.name
                                           );
                                      valueCode.value =  method;
                                    final methodRepository =  apiRepository(
                                                   method.name??'',
                                                   entityClasses.firstOrNull?.name??'',
                                                   jsonBodyParamsContent.firstOrNull?.name??''
                                                   );
                                     valueCodeRepo.value = methodRepository;
    final className = '${vMethod.name.pascalCase}${_featuresController.text.pascalCase}';
                                       final methodUsecase =  apiUseCase(
                                        vMethod.name,
                                      className,
                                     _controllerRepository.text.isEmpty
                                      ? '${_featuresController.text.pascalCase}Repository':
                                      _controllerRepository.text,
                                                 methodName ,
                                                   entityClasses.firstOrNull?.name??'',
                                                  paramNameCommon: jsonBodyParamsContent.firstOrNull?.name
                                                   );
                                        
                                    valueCodeUsecase.value = methodUsecase;
                                    final eventBloc = createEventBloc(
                                      vMethod.name,
                                       vMethod.name,
                                      className,
                                      paramNameCommon: jsonBodyParamsContent.firstOrNull?.name
                                    );
                                    vEventBloc.value = eventBloc;
                                      }
                                   
                                    
                                      return vMethod == null?
                                      const SizedBox():
                                       Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                      width: 200,
                                      height: 42,
                                      child: TextField(
                                        controller: _controllerMethodName,
                                        style: TextStyle(color: Colors.white),
                                        decoration: InputDecoration(
                                          hintText: 'tên hàm....',
                                          filled: true,
                                          fillColor: Colors.black
                                        ),
                                        onChanged: (value){
                                            final methodName = value.isNotEmpty
                                            ?value
                                            :'${vMethod.name.toLowerCase()}'
                                      '${_featuresController.text}';
                                     final method =  apiMethod45(vMethod.name,
                                           segments: params,
                                           nameMethod: methodName,
                                           paramName: jsonParamsContent.firstOrNull?.name,
                                           bodyName: jsonBodyParamsContent.firstOrNull?.name,
                                           modelName: jsonModelClasses.firstOrNull?.name
                                           );
                                      valueCode.value =  method;
                                    final methodRepository =  apiRepository(
                                                   method.name??'',
                                                   entityClasses.firstOrNull?.name??'',
                                                   jsonBodyParamsContent.firstOrNull?.name??''
                                                   );
                                         valueCodeRepo.value = methodRepository;
                                     final className = '${vMethod.name.pascalCase}${_featuresController.text.pascalCase}';
                                    final methodUsecase =  apiUseCase(
                                      vMethod.name,
                                      className,
                                      _controllerRepository.text.isEmpty
                                      ? '${_featuresController.text.pascalCase}Repository':
                                      _controllerRepository.text,
                                                 methodName,
                                                   entityClasses.firstOrNull?.name??'',
                                                  paramNameCommon: jsonBodyParamsContent.firstOrNull?.name
                                                   );
                                    
                                    valueCodeUsecase.value = methodUsecase;
                                    
                                        },
                                      ),),
                                       ValueListenableBuilder(
                                                 valueListenable: valueCode,
                                                  builder: (context, vCode, child) {
                                             final code = JsonToModelGenerator.genContentMethod(vCode ?? Method()).replaceAll('{}', ';');
                                              return Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  SizedBox(
                                                    width: 600,
                                                    child: UniversalCodeViewer(
                                        codeLanguage: 'Dart',
                                          code: code,
                                       style: SyntaxHighlighterStyles.vscodeDark,
                                                                  ),
                                                  ),
                                                ],
                                              );
                                         }
                                       ),
                                        ValueListenableBuilder(
                                                        valueListenable: valueCodeRepo,
                                                       builder: (context, vCode, child) {
                                                final code =  JsonToModelGenerator.genContentMethod(vCode ?? Method()).replaceAll('{}', ';');
                                            return SizedBox(
                                                 width: 600,
                                              child: UniversalCodeViewer(
                                              codeLanguage: 'Dart',
                                              code: code,
                                              style: SyntaxHighlighterStyles.vscodeDark,
                                                      ),
                                            );
                                              }),
                                        ],
                                      );
                                    }
                                  ),
                             
                               Column(
                                 children: [
                                    SizedBox(
                                      width: 200,
                                      height: 42,
                                      child:  ValueListenableBuilder(
                                    valueListenable: _typeMethod,
                                    builder: (context, vMethod, child) {
                                          return TextField(
                                            controller: _controllerRepository,
                                            style: TextStyle(color: Colors.white),
                                            decoration: InputDecoration(
                                              hintText: 'Repository Name....',
                                              filled: true,
                                              fillColor: Colors.black
                                            ),
                                            onChanged: (value) {
                                              final className = '${vMethod?.name.pascalCase}${_featuresController.text.pascalCase}';
                                               final methodUsecase =  apiUseCase(
                                          '',
                                          className,
                                          value,
                                                   _controllerMethodName.text.isNotEmpty?
                                                    _controllerMethodName.text:
                                                     '${vMethod?.name.camelCase}${_featuresController.text}',
                                                       entityClasses.firstOrNull?.name??'',
                                                      paramNameCommon: jsonBodyParamsContent.firstOrNull?.name
                                                       );
                                                                              
                                                  valueCodeUsecase.value = methodUsecase;
                                            },
                                          );
                                        }
                                      ),
                                    ),
                                   ValueListenableBuilder(
                                        valueListenable: valueCodeUsecase,
                                        builder: (context, vCode, child) {
                                          String code = '';
                                          if(vCode != null){
                                             final createOnMethod = methodBloc(vCode.name);
                                             vOnMethodBloc.value = createOnMethod;
                                              code =  JsonToModelGenerator.genContentClass(vCode).replaceAll('{}', ';');
                                          
                                          }
                                   
                                          return vCode !=null? SizedBox(
                                            width: 600,
                                            child: UniversalCodeViewer(
                                              code: code,
                                                codeLanguage: 'Dart',
                                                    style: SyntaxHighlighterStyles.vscodeDark,// Enable/Disable container expansion
                                                      ),
                                          ):SizedBox();
                                        }),
                                Container(
                                  width: 600,
                            
                                  child: Row(
                                    children: [
                                      Expanded(child: ValueListenableBuilder(
                                        valueListenable: vEventBloc,
                                        builder: (context, vEvent, child) {
                                          String code = '';
                                          if(vEvent != null){
        
 code =  JsonToModelGenerator.genContentClass(vEvent).replaceAll('{}', ';');

                                          }
                                          return UniversalCodeViewer(
                                                  code: code,
                                                    codeLanguage: 'Dart',
                                                        style: SyntaxHighlighterStyles.vscodeDark,// Enable/Disable container expansion
                                                          );
                                        }
                                      ),),
                                      Expanded(child:ValueListenableBuilder(
                                        valueListenable: vOnMethodBloc,
                                        builder: (context, vStateBloc, child) {
                                                    String code = '';
                                          if(vStateBloc != null){
        
 code =  JsonToModelGenerator.genContentMethod(vStateBloc).replaceAll('{}', ';');

                                          }
                                          return UniversalCodeViewer(
                                                  code: code,
                                                    codeLanguage: 'Dart',
                                                        style: SyntaxHighlighterStyles.vscodeDark,// Enable/Disable container expansion
                                                          );
                                        }
                                      ),
                                      )
                                    ],
                                  )
                                )
                                 ],
                               ),
                              
                               ],
                             );
                           }
                         ),
                         
                       ],
                     ),
                   ),
                 
                ],
              ),
            ),
          ),
       
        ],
      ),
    );
  }

  Widget _buildTextField(TextEditingValue vFeature, TypeKey vType) {
    return JsonTextfield(
      controllerCode: _controllerCode,
      readOnly:  vFeature.text.isEmpty,
      onChange: (){
                _genModel(vType);
      });
  
 
  }

  void _genModel(TypeKey vType) {
     final jsonPath = {"data": jsonDecode(_controllerCode.text.isEmpty?'{}'
              :_controllerCode.text) };
                                 jsonModelClasses = JsonToModelGenerator.generate(
    initialClassName: _featuresController.text.camelCase,
    json: jsonPath,
    isPascal: vType == TypeKey.pascal
                                );
                               
                                 entityClasses =
      jsonModelClasses.map((final e) => e.toEntityClass()).toList();
  }
  Method apiMethod45(
    final String method,
  {
    final List<String>? segments,
    final String? nameMethod,
    final String? paramName, 
    final String? bodyName,
  final String? modelName,
  final bool isMultipart = false,
  }) {
    methodRepositoryName = nameMethod??'$method$nameMethod'.camelCase;
  return Method(
    (final b) => b
      ..returns = refer("Future<HttpResponse<GenericResponseModel<${modelName?.isEmpty ?? false
      ? 'NoDataModel':modelName }>?>>")
      ..requiredParameters.addAll(
       [
     if(segments != null)
      for(final seg in segments)
         Parameter(
          (final b) => b
            ..name = seg
            ..type = refer('@Path() final String'),
        ),
     if(paramName != null) 
         Parameter(
          (final b) => b
            ..name = 'query'
            ..type = refer('@Queries() final $paramName'),
        ),
    if(bodyName != null && (method == "POST"||method == "PUT") )  
     Parameter(
          (final b) => b
            ..name = 'body'
            ..type = refer('@Body() final $bodyName'),
        ),
  if(isMultipart)  Parameter(
          (final b) => b
            ..name = 'files'
            ..type = refer('@Part() final List<MultipartFile>'),
        ),
        ]
      )..optionalParameters.addAll( 
       [
         if(isMultipart)...[
      Parameter(
          (final b) => b
            ..name = 'onSendProgress'
            ..type = refer('@SendProgress() final ProgressCallback?'),
        ),
      Parameter(
          (final b) => b
            ..name = 'onCancelRequest'
            ..type = refer('@CancelRequest() final CancelToken?'),
        ),  
  ],
        Parameter((b) => b
        ..name = 'isMockUp'
        ..type = refer("@Header('isMockUp') final bool?")
        ..named = true
        )],)
      ..name = methodRepositoryName
      ..body = Code('')
      ..annotations.addAll([
         refer("${method.pascalCase}('EndPoints.')"),
         if(isMultipart) refer('MultiPart()')
      ])
  );
}
  Method apiRepository(
  final String nameMethod,
  final String entitiesName,
  final String paramNameCommon
  ) {
  return Method(
    (final b) => b
      ..returns = refer('Future<DataState<$entitiesName?>>')
      ..requiredParameters.addAll(
       [ 
         Parameter(
          (final b) => b
            ..name = 'param'
            ..type = refer('final $paramNameCommon'),
        ),
   
        ]
      )
      ..name = nameMethod
      ..body = Code('')
  );
}
 Method methodBloc(
    final String nameUseCase,
  {
    
  final bool isMultipart = false,
  }) {
   
     return Method(
    (final b) => b
      ..returns = refer("FutureOr<void>")
      ..name = '_on${nameUseCase.pascalCase}'
      ..requiredParameters.addAll(
       [

       ]
       )..body = Code('''
    emit(state.copyWith(${_featuresController.text.pascalCase}Status.loading));
    final dataState = await _${nameUseCase.camelCase}(
      params: event.params,
    );
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ${_featuresController.text.pascalCase}Status.${nameUseCase.camelCase}Success,
          data: dataState.data,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ${_featuresController.text.pascalCase}Status.faiure,
          data: dataState.error,
        ),
      );
    }
''')
       );
        
  }
Class apiUseCase(
   final String method,
  final String className,
  final String repositoryName,
  final String? nameMethod,
  final String entitiesName,
  {final String? paramNameCommon}
  ) {
    methodRepositoryName = nameMethod
    ??'$method$nameMethod'.camelCase;
  return Class(
      (final b) => b
        ..name = '${className}Usecae'
        ..implements.add(
          refer('UseCase<DataState<$entitiesName?>, ${paramNameCommon ?? 'void'}>'))
        ..constructors.add(
          Constructor(
            (final constructor) => constructor
              ..requiredParameters.add(Parameter((p)
              =>p..toThis = true
              ..name = '_repository' ))
          ),
        )
        ..annotations.addAll([
          refer('injectable'),
        ])
        ..fields.add(Field((b) =>
        b
        ..name = '_repository'
        ..type = refer('final $repositoryName')
         ,))
        ..methods.addAll([
          Method(
            (m)=> m..annotations.add(refer('override'))
            ..returns = refer('Future<DataState<$entitiesName?>>') 
            ..name = 'call'
            ..body = Code('return _repository.$methodRepositoryName(params);')
            ..optionalParameters.add(Parameter((p)
            =>p..name = 'params'
            ..type = refer('final ${paramNameCommon??'void'}')
            ..required = true
            ..named = true
            ))
          )
        ])
        
    );
}
Class createEventBloc(
   final String method,
  final String className,
  final String? nameMethod,
  {final String? paramNameCommon}
  ) {
    methodRepositoryName = nameMethod??'$method$nameMethod'.pascalCase;
  return Class(
      (final b) => b
        ..name = '${methodRepositoryName}Event'
        ..extend = refer('${_featuresController.text}Event')
        ..constructors.add(
          Constructor(
            (final constructor) => constructor
              ..requiredParameters.add(Parameter((p)
              =>p..toThis = true
              ..name = 'param' ))
          ),
        )
        ..fields.add(Field((b) =>
        b
        ..name = 'param'
        ..type = refer('final ${paramNameCommon ?? 'void'} ')
         ,))
        
    );
}

  final Map<String, dynamic> json = {
  "greeting": "Welcome to quicktype!",
  "path": "/quicktype/{id}/{partId}?share=1&ref=quicktype",
  "body":{
    "val": 1,
    "val2": "test string",
    "val3": true,
    "val4": 1.0
  },
    'data': {
      "items": [
        {
          "AppraisalPeriodId": 3,
          "AppraisalPeriodName": "Đánh giá đột xuất - CSKH (Test)",
          "AppraisalTypeName": "Đánh giá đột xuất",
          "TranYear": 2023,
          "FromDate": "01/12/2023"
        },
        {
          "AppraisalPeriodId": 3,
          "AppraisalPeriodName": "Đánh giá đột xuất - CSKH (Test)",
          "AppraisalTypeName": "Đánh giá đột xuất",
          "TranYear": 2023,
          "FromDate": "01/12/2023",
        }
      ],
    },
  };

  
  
}
