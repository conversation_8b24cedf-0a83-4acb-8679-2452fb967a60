import 'dart:async';
import 'dart:convert';

import 'package:code_builder/code_builder.dart';
import 'package:flutter/material.dart';
import 'package:mason/mason.dart';
import 'package:re_editor/re_editor.dart';
import 'package:testwidget/helpers/class_extension.dart';
import 'package:testwidget/json_model_gen.dart';
import 'package:testwidget/style_table_textfield.dart';
import 'package:testwidget/widgets/json_input_section.dart' show JsonInputSection, TypeKey;
import 'package:testwidget/widgets/api_method_section.dart' show ApiMethodSection, TypeMethod;
import 'package:testwidget/widgets/code_generation_section.dart';
import 'package:testwidget/widgets/responsive_layout.dart';
import 'package:testwidget/widgets/modern_app_bar.dart';
import 'package:testwidget/theme/theme.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _isDarkMode = false;

  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Code Generator',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: _isDarkMode ? ThemeMode.dark : ThemeMode.light,
      debugShowCheckedModeBanner: false,
      home: MyHomePage(
        title: 'Code Generator',
        isDarkMode: _isDarkMode,
        onThemeToggle: _toggleTheme,
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({
    super.key,
    required this.title,
    required this.isDarkMode,
    required this.onThemeToggle,
  });

  final String title;
  final bool isDarkMode;
  final VoidCallback onThemeToggle;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  // Controllers
  late TextEditingController _featuresController;
  late TextEditingController _controllerPath;
  late TextEditingController _controllerBody;
  late TextEditingController _controllerRepository;
  late TextEditingController _controllerMethodName;
  late CodeLineEditingController _controllerCode;
  late StyleableTextFieldController _controller;

  // Value Notifiers
  final ValueNotifier<TypeKey> _typeKey = ValueNotifier(TypeKey.pascal);
  final ValueNotifier<TypeMethod?> _typeMethod = ValueNotifier(null);
  final ValueNotifier<Method?> valueCode = ValueNotifier(null);
  final ValueNotifier<Method?> valueCodeRepo = ValueNotifier(null);
  final ValueNotifier<Class?> valueCodeUsecase = ValueNotifier(null);
  final ValueNotifier<Class?> vEventBloc = ValueNotifier(null);
  final ValueNotifier<Method?> vOnMethodBloc = ValueNotifier(null);

  // Data
  List<Class> jsonParamsContent = [];
  List<Class> jsonBodyParamsContent = [];
  List<Class> jsonModelClasses = [];
  List<Class> entityClasses = [];
  late Method apiMethod;
  String methodName = '';
  String methodRepositoryName = '';

  // Overlay
  OverlayEntry? _overlayEntry;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _featuresController = TextEditingController();
    _controllerPath = TextEditingController();
    _controllerBody = TextEditingController();
    _controllerRepository = TextEditingController();
    _controllerMethodName = TextEditingController();
    _controllerCode = CodeLineEditingController(
      options: CodeLineOptions(),
      spanBuilder: _buildJsonSpan,
    );
    _controller = StyleableTextFieldController(
      text: '',
      styles: TextPartStyleDefinitions(
        definitionList: <TextPartStyleDefinition>[
          TextPartStyleDefinition(
            style: TextStyle(color: Colors.green),
            pattern: r'[{}]',
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _featuresController.dispose();
    _controllerPath.dispose();
    _controllerBody.dispose();
    _controllerRepository.dispose();
    _controllerMethodName.dispose();
    _controllerCode.dispose();
    _controller.dispose();
    _debounce?.cancel();
    _hideOverlay();
    super.dispose();
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _showOverlay(Widget body, double paddingTop, [double height = 200]) {
    final renderBox = context.findRenderObject() as RenderBox?;
    final offset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx + 400,
        top: offset.dy + paddingTop,
        child: Material(
          child: MouseRegion(
            onEnter: (event) {
              if (_debounce?.isActive ?? false) {
                _debounce?.cancel();
              }
            },
            onExit: (event) {
              _hideOverlay();
            },
            child: SizedBox(
              height: height,
              child: body,
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _genModel(TypeKey vType) {
    final jsonPath = {
      "data": jsonDecode(_controllerCode.text.isEmpty
          ? '{}'
          : _controllerCode.text)
    };

    jsonModelClasses = JsonToModelGenerator.generate(
      initialClassName: _featuresController.text.pascalCase,
      json: jsonPath,
      isPascal: vType == TypeKey.pascal,
    );

    entityClasses = jsonModelClasses
        .map((final e) => e.toEntityClass())
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveScaffold(
      appBar: ModernAppBar(
        featuresController: _featuresController,
        onThemeToggle: widget.onThemeToggle,
        isDarkMode: widget.isDarkMode,
      ),
      body: ResponsiveSideBySide(
        leftFlex: 2,
        rightFlex: 3,
        spacing: AppSpacing.lg,
        left: JsonInputSection(
          featuresController: _featuresController,
          controllerPath: _controllerPath,
          controllerBody: _controllerBody,
          controllerCode: _controllerCode,
          typeKey: _typeKey,
          onJsonParamsChanged: (params) {
            setState(() {
              jsonParamsContent = params;
            });
          },
          onJsonBodyParamsChanged: (bodyParams) {
            setState(() {
              jsonBodyParamsContent = bodyParams;
            });
          },
          onJsonModelChanged: (modelClasses, entities) {
            setState(() {
              jsonModelClasses = modelClasses;
              entityClasses = entities;
            });
          },
          onTypeKeyChanged: (typeKey) {
            _genModel(typeKey);
          },
          showOverlay: _showOverlay,
          hideOverlay: _hideOverlay,
        ),
        right: Column(
          children: [
            ApiMethodSection(
              featuresController: _featuresController,
              controllerPath: _controllerPath,
              controllerMethodName: _controllerMethodName,
              typeMethod: _typeMethod,
              onMethodChanged: _handleMethodChanged,
              onMethodNameChanged: _handleMethodNameChanged,
              jsonParamsContent: jsonParamsContent,
              jsonBodyParamsContent: jsonBodyParamsContent,
              jsonModelClasses: jsonModelClasses,
            ),
            SizedBox(height: AppSpacing.md),
            Expanded(
              child: CodeGenerationSection(
                apiMethod: valueCode,
                repositoryMethod: valueCodeRepo,
                useCaseClass: valueCodeUsecase,
                eventBlocClass: vEventBloc,
                onMethodBloc: vOnMethodBloc,
                controllerRepository: _controllerRepository,
                onRepositoryNameChanged: _handleRepositoryNameChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMethodChanged(TypeMethod method, String methodName) {
    // Simple implementation - can be expanded later
    debugPrint('Method changed: ${method.name}, $methodName');
  }

  void _handleMethodNameChanged(String methodName) {
    // Simple implementation - can be expanded later
    debugPrint('Method name changed: $methodName');
  }

  void _handleRepositoryNameChanged(String repositoryName) {
    // Simple implementation - can be expanded later
    debugPrint('Repository name changed: $repositoryName');
  }

  TextSpan _buildJsonSpan({
    required codeLine,
    required context,
    required index,
    required style,
    required textSpan
  }) {
    // Simple JSON syntax highlighting implementation
    return TextSpan(style: style, text: codeLine.text);
  }
}