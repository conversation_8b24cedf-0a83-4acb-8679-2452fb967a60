import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'color.dart';

class AppTextStyles {
  // Base font family
  static String get fontFamily => GoogleFonts.inter().fontFamily!;

  // Light theme text styles
  static TextTheme get textTheme => TextTheme(
  // Header 1 - Bold 24px
  displayLarge: GoogleFonts.inter(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    height: 38 / 24, // line-height / font-size
    letterSpacing: 0,
  ),
  
  // Header 2 - Medium 20px
  displayMedium: GoogleFonts.inter(
    fontSize: 20,
    fontWeight: FontWeight.w500,
    height: 33 / 20,
    letterSpacing: 0,
  ),
  
  // Header 3 - Medium 18px
  displaySmall: GoogleFonts.inter(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 22 / 18,
    letterSpacing: 0,
  ),
  
  // Header 4 - Medium 17px
  headlineLarge: GoogleFonts.inter(
    fontSize: 17,
    fontWeight: FontWeight.w500,
    height: 21 / 17,
    letterSpacing: 0,
  ),
  
  // Header 5 - Medium 16px
  headlineMedium: GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 20 / 16,
    letterSpacing: 0,
  ),
  
  // Header 6 - Medium 15px
  headlineSmall: GoogleFonts.inter(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    height: 18 / 15,
    letterSpacing: 0,
  ),
  
  // Title - Medium 15px
  titleLarge: GoogleFonts.inter(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    height: 18 / 15,
    letterSpacing: 0,
  ),
  
  // Body Regular - Regular 15px
  bodyLarge: GoogleFonts.inter(
    fontSize: 15,
    fontWeight: FontWeight.w400,
    height: 18 / 15,
    letterSpacing: 0,
  ),
  
  // Sub Medium - Medium 14px
  bodyMedium: GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 17 / 14,
    letterSpacing: 0,
  ),
  
  // Sub - Regular 14px
  bodySmall: GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 17 / 14,
    letterSpacing: 0,
  ),
  
  // Caption Bold - Bold 13px
  titleMedium: GoogleFonts.inter(
    fontSize: 13,
    fontWeight: FontWeight.bold,
    height: 15 / 13,
    letterSpacing: 0,
  ),
  
  // Caption - Regular 13px
  titleSmall: GoogleFonts.inter(
    fontSize: 13,
    fontWeight: FontWeight.w400,
    height: 15 / 13,
    letterSpacing: 0,
  ),
  
  // Body Small - Regular 12px
  labelLarge: GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 14 / 12,
    letterSpacing: 0,
  ),
  
  // Sub Small - Bold 12px
  labelMedium: GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    height: 14 / 12,
    letterSpacing: 0,
  ),
  
  // Description Small - Regular 10px
  labelSmall: GoogleFonts.inter(
    fontSize: 10,
    fontWeight: FontWeight.w400,
    height: 12 / 10,
    letterSpacing: 0,
  ),
);

  // Dark theme text styles
  static TextTheme get darkTextTheme {
    return textTheme.apply(
      bodyColor: Colors.white70,
      displayColor: Colors.white,
    );
  }

  // Convenience getters for commonly used styles
  static TextStyle get displayLarge => textTheme.displayLarge!;
  static TextStyle get displayMedium => textTheme.displayMedium!;
  static TextStyle get displaySmall => textTheme.displaySmall!;

  static TextStyle get headlineLarge => textTheme.headlineLarge!;
  static TextStyle get headlineMedium => textTheme.headlineMedium!;
  static TextStyle get headlineSmall => textTheme.headlineSmall!;

  static TextStyle get titleLarge => textTheme.titleLarge!;
  static TextStyle get titleMedium => textTheme.titleMedium!;
  static TextStyle get titleSmall => textTheme.titleSmall!;

  static TextStyle get bodyLarge => textTheme.bodyLarge!;
  static TextStyle get bodyMedium => textTheme.bodyMedium!;
  static TextStyle get bodySmall => textTheme.bodySmall!;

  static TextStyle get labelLarge => textTheme.labelLarge!;
  static TextStyle get labelMedium => textTheme.labelMedium!;
  static TextStyle get labelSmall => textTheme.labelSmall!;
}