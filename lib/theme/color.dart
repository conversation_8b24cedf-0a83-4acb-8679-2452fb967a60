import 'package:flutter/material.dart';

class AppColors {

   static ColorScheme get lightTheme {
    return const ColorScheme.light(
  brightness: Brightness.light,

  // Màu chủ đạo
  primary: AppColors.primary400, // <PERSON><PERSON><PERSON> xanh lá cây cho các nút và icon chính
  onPrimary: Colors.white,       // Chữ màu trắng trên nền xanh lá để nổi bật

  // <PERSON><PERSON><PERSON>hụ (thường dùng cho các thành phần ít quan trọng hơn)
  secondary: AppColors.primary600,
  onSecondary: Colors.white,

  // <PERSON><PERSON>u lỗi (dành cho các nút/trạng thái nguy hiểm, từ chối)
  error: AppColors.danger500,
  onError: Colors.white,        

  surface: Colors.white,         // <PERSON><PERSON>u của các thẻ (Card), dialog. Thường là màu trắng.
  onSurface: AppColors.black800, // <PERSON><PERSON><PERSON> chữ trên các thẻ

  // (<PERSON><PERSON><PERSON> chọn) <PERSON><PERSON><PERSON> sắc cho các trạng thái khác
  tertiary: AppColors.warning500,
  onTertiary: Colors.white,
      );
    
    }

  // Primary Colors
  static const Color primary50 = Color(0xFFEFEFEF);
  static const Color primary100 = Color(0xFFE0E7CF);
  static const Color primary200 = Color(0xFFC6CF63);
  static const Color primary400 = Color(0xFF7BC240);
  static const Color primary500 = Color(0xFF6CF563);
  static const Color primary600 = Color(0xFF6AA668);

  // Secondary Colors
  static const Color secondary50 = Color(0xFFF7FA1B);
  static const Color secondary100 = Color(0xFFEEF264);
  static const Color secondary400 = Color(0xFFAACA49);
  static const Color secondary500 = Color(0xFF8F9F30);
  static const Color secondary600 = Color(0xFF7D8B28);

  // Black/Gray Scale
  static const Color black50 = Color(0xFFF6F6F6);
  static const Color infer70 = Color(0xFFF3F3F3);
  static const Color infer100 = Color(0xFFE3E3E3);
  static const Color black200 = Color(0xFFCCCCCC);
  static const Color black400 = Color(0xFF888888);
  static const Color black500 = Color(0xFF666666);
  static const Color black700 = Color(0xFF525252);
  static const Color black800 = Color(0xFF313131);
  static const Color black850 = Color(0xFF2C2C2C);
  static const Color black900 = Color(0xFF0D0D0D);

  // Success Colors
  static const Color success50 = Color(0xFFE5CCD5);
  static const Color success100 = Color(0xFF88E4A3);
  static const Color success200 = Color(0xFF5CD98A);
  static const Color success400 = Color(0xFF3FB637);
  static const Color success500 = Color(0xFF33A655);

  // Danger Colors
  static const Color danger50 = Color(0xFFFEE8EA);
  static const Color danger100 = Color(0xFFF7C8C5);
  static const Color danger200 = Color(0xFFE98A6C);
  static const Color danger400 = Color(0xFFE6359F);
  static const Color danger500 = Color(0xFFF53B5D);

  // Warning Colors
  static const Color warning50 = Color(0xFFFEE8E5);
  static const Color warning100 = Color(0xFFFEDDCC);
  static const Color warning200 = Color(0xFFFDAAA9);
  static const Color warning400 = Color(0xFFEB8515);
  static const Color warning500 = Color(0xFFFA9005);

  // Info Colors
  static const Color info50 = Color(0xFFE1E4FF);
  static const Color info100 = Color(0xFFCC67F7);
  static const Color info400 = Color(0xFF648EFF);
  static const Color info500 = Color(0xFF5D7AFF);
  static const Color info600 = Color(0xFF5D519B);
}