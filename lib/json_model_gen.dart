import 'package:code_builder/code_builder.dart';
import 'package:dart_style/dart_style.dart';
import 'package:mason/mason.dart';
import 'package:testwidget/helpers/json_extension.dart';
import 'package:testwidget/helpers/map_entry_extension.dart';
import 'package:testwidget/models/named_map.dart';

final _dartfmt = DartFormatter();

class JsonToModelGenerator {
  /// generate Model classes
  static List<Class> generate({
    required final String initialClassName,
    required final Map<String, dynamic> json,
    final bool isPascal = true,
  }) {
    final totalMaps = getNestedClassMaps(
      initialClassName: initialClassName,
      data: json.getJsonFromDataKey,
    );

    final classes = totalMaps
        .map(
          (final m) => m.toModelClass(isPascal: isPascal),
        )
        .toList();

    return classes;
  }

  static List<Class> generateParamBody({
    required final String initialClassName,
    required final Map<String, dynamic> json,
  }) {
    final totalMaps = getNestedClassMaps(
      initialClassName: initialClassName,
      data: json.getJsonBodyFromDataKey,
      namePart: 'BodyParams',
    );

    final classes = totalMaps.map((final m) => m.toParamClass()).toList();

    return classes;
  }

  static List<Class> generateParam({
    required final String initialClassName,
    required final Map<String, dynamic> json,
  }) {
    final totalMaps = getNestedClassMaps(
      initialClassName: initialClassName,
      data: json.getParams,
      namePart: 'QueryParams',
    );

    final classes = totalMaps.map((final m) => m.toParamClass()).toList();

    return classes;
  }

  /// generate file content from classes
  static String genContentFrom(final List<Class> classes,
      [final List<Directive> imports = const []]) {
    final emitter = DartEmitter();
    final importContent =
        imports.map((final e) => e.accept(emitter)).join('\n');
    // Convert class definitions to string
    final classContent = classes.map((e) => e.accept(emitter)).join('\n\n');

    final stringContent = '$importContent\n\n$classContent';
    //     classes.map((final e) => e.accept(DartEmitter())).toList().join();

    return _dartfmt.format(stringContent);
  }

  static String genOnlyContent(final Code code) {
    final stringContent = code.accept(DartEmitter(useNullSafetySyntax: true));
    return _dartfmt.format(stringContent.toString());
  }

  static String genContentMethod(final Method method) {
    final stringContent = method.accept(DartEmitter(useNullSafetySyntax: true));
    return _dartfmt.format(stringContent.toString());
  }

  static String genContentClass(final Class method) {
    final stringContent = method.accept(DartEmitter(useNullSafetySyntax: true));
    return _dartfmt.format(stringContent.toString());
  }

  /// generate file content
  static String genMapperContent(final List<Class> modelClasses) {
    //   final orgMapperFileContent = Utils.getMapperFileContent();
    final mapperContent = modelClasses
        //  .where((final e) => !orgMapperFileContent.contains(e.name))
        .map((final e) {
          final genMapperLineCode =
              'MapType<${e.name}, ${e.name.replaceAll('Model', '')}>(),\n';
          return genMapperLineCode;
        })
        .toList()
        .join();

    return mapperContent;
  }

  static List<NamedMap> getNestedClassMaps(
      {required final String initialClassName,
      required final Map<String, dynamic> data,
      final String namePart = 'Model'}) {
    if (data.isNotEmpty) {
      // nested classes under the big json
      final nestedClassMaps = data.entries
          .where((final entry) => entry.isValueCustomDataType)
          .map(
            (final e) => NamedMap(
              name: '${initialClassName.replaceAll('Model', '')}'
                  '${e.key.pascalCase}$namePart',
              data: e.getMapData,
            ),
          )
          .toList();

      final nextLevelNestedClasses = <NamedMap>[];
      for (var i = 0; i < nestedClassMaps.length; i++) {
        nextLevelNestedClasses.addAll(
          getNestedClassMaps(
            initialClassName: nestedClassMaps[i].name,
            data: nestedClassMaps[i].data,
            namePart: namePart,
          )..removeAt(0),
        );
      }

      // total maps that need to be generated into classes
      final totalMaps = [
        NamedMap(
          data: data,
          name: '${initialClassName.pascalCase}$namePart',
        ),
        ...nestedClassMaps,
        ...nextLevelNestedClasses,
      ];

      return totalMaps;
    }
    return [];
  }
}
