import 'package:code_builder/code_builder.dart';
import 'package:mason/mason.dart';

import '../helpers/string_extension.dart';
import '../utils/utils.dart';

/// Json map with it's name
class NamedMap {
  NamedMap({
    required this.name,
    required this.data,
  });

  final String name;
  final Map<String, dynamic> data;

  Class toModelClass({final bool isPascal = true}) {
    final classNameFormatted = name.pascalCase;

    return Class(
      (final b) => b
        ..name = classNameFormatted
        ..constructors.add(
          Constructor(
            (final constructor) => constructor
              ..optionalParameters.addAll(
                data.keys
                    .map(
                      (final key) => Parameter(
                        (final param) => param
                          ..name = key.camelCase
                          ..toThis = true
                          ..required = data[key] is List
                          ..named = true,
                      ),
                    )
                    .toList(),
              ),
          ),
        )
        ..annotations.addAll([
          refer('JsonSerializable(createToJson: false)'),
        ])
        ..methods.addAll([
          jsonFromMethod(classNameFormatted),
        ])
        ..fields.addAll(
          data.keys
              .map(
                (final key) => Field(
                  (final b) => b
                    ..name = key.camelCase
                    ..annotations.addAll([
                      if (key.isDisplayJsonSerializeAnnotate)
                        refer('JsonKey').call([], {
                          'name': literalString(
                            isPascal ? key.pascalCase : key.snakeCase,
                          ),
                        }),
                    ])
                    ..type = refer(
                      'final ${Utils.getValueType(
                        modelName: classNameFormatted,
                        entry: MapEntry(key, data[key]),
                      )}',
                    ),
                ),
              )
              .toList(),
        ),
    );
  }

  Class toParamClass() {
    final classNameFormatted = name.pascalCase;

    return Class(
      (final b) => b
        ..name = classNameFormatted
        ..constructors.add(
          Constructor(
            (final constructor) => constructor
              ..optionalParameters.addAll(
                data.keys
                    .map(
                      (final key) => Parameter(
                        (final param) => param
                          ..name = key.camelCase
                          ..toThis = true
                          ..required = data[key] is List
                          ..named = true,
                      ),
                    )
                    .toList(),
              ),
          ),
        )
        ..annotations.addAll([
          refer('JsonSerializable(createFactory: false,includeIfNull: false,)'),
        ])
        ..methods.addAll([
          jsonToMethod(classNameFormatted),
        ])
        ..fields.addAll(
          data.keys
              .map(
                (final key) => Field(
                  (final b) => b
                    ..name = key.camelCase
                    ..annotations.addAll([
                      if (key.isDisplayJsonSerializeAnnotate)
                        refer('JsonKey').call([], {
                          'name': literalString(key),
                        }),
                    ])
                    ..type = refer(
                      'final ${Utils.getValueType(
                        modelName: classNameFormatted,
                        entry: MapEntry(key, data[key]),
                      )}',
                    ),
                ),
              )
              .toList(),
        ),
    );
  }
}

Method jsonFromMethod(final String className) {
  final adaptiveColon = (className.length > 24) ? ',' : '';

  return Method(
    (final b) => b
      ..lambda = true
      ..body = Code('_\$${className}FromJson(json)')
      ..requiredParameters.add(
        Parameter(
          (final b) => b
            ..name = 'json$adaptiveColon'
            ..type = refer('final Map<String, dynamic>'),
        ),
      )
      ..name = '$className.fromJson'
      ..returns = refer('factory'),
  );
}

Method jsonToMethod(final String className) {
  // final adaptiveColon = (className.length > 24) ? ',' : '';

  return Method(
    (final b) => b
      ..lambda = true
      ..body = Code('_\$${className}ToJson(this)')
      ..name = 'toJson'
      //  ..name = '$className.fromJson'
      ..returns = refer('Map<String, dynamic>'),
  );
}
