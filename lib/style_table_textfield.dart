// Flutter imports:
import 'package:flutter/material.dart';

class StyleableTextFieldController extends TextEditingController {
  StyleableTextFieldController({
    super.text,
    required this.styles,
  }) : combinedPattern = styles.createCombinedPatternBasedOnStyleMap();

  final TextPartStyleDefinitions styles;
  final Pattern combinedPattern;

  @override
  TextSpan buildTextSpan({
    required final BuildContext context,
    final TextStyle? style,
    required final bool withComposing,
  }) {
    final List<InlineSpan> textSpanChildren = <InlineSpan>[];

    // Theo dõi độ sâu của ngoặc
    int curlyBraceDepth = 0;
    int squareBracketDepth = 0;

    // <PERSON><PERSON>u sắc cho các cấp độ khác nhau
    final List<Color> curlyBraceColors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    final List<Color> squareBracketColors = [
      Colors.cyan,
      Colors.pink,
      Colors.amber,
      Colors.indigo,
      Colors.lime,
      Colors.brown,
    ];

    // <PERSON>yệt qua từng ký tự
    for (int i = 0; i < text.length; i++) {
      final String char = text[i];
      TextStyle? currentStyle = style;

      if (char == '{') {
        // Tô màu dấu { theo cấp độ hiện tại
        final Color color =
            curlyBraceColors[curlyBraceDepth % curlyBraceColors.length];
        currentStyle = style?.copyWith(color: color) ?? TextStyle(color: color);
        curlyBraceDepth++;
      } else if (char == '}') {
        // Giảm cấp độ trước khi tô màu
        curlyBraceDepth =
            (curlyBraceDepth - 1).clamp(0, double.infinity).toInt();
        final Color color =
            curlyBraceColors[curlyBraceDepth % curlyBraceColors.length];
        currentStyle = style?.copyWith(color: color) ?? TextStyle(color: color);
      } else if (char == '[') {
        // Tô màu dấu [ theo cấp độ hiện tại
        final Color color = squareBracketColors[
            squareBracketDepth % squareBracketColors.length];
        currentStyle = style?.copyWith(color: color) ?? TextStyle(color: color);
        squareBracketDepth++;
      } else if (char == ']') {
        // Giảm cấp độ trước khi tô màu
        squareBracketDepth =
            (squareBracketDepth - 1).clamp(0, double.infinity).toInt();
        final Color color = squareBracketColors[
            squareBracketDepth % squareBracketColors.length];
        currentStyle = style?.copyWith(color: color) ?? TextStyle(color: color);
      }

      // Thêm ký tự vào TextSpan
      textSpanChildren.add(
        TextSpan(
          text: char,
          style: currentStyle,
        ),
      );
    }

    return TextSpan(style: style, children: textSpanChildren);
  }
}

class TextPartStyleDefinition {
  TextPartStyleDefinition({
    required this.pattern,
    required this.style,
  });
  final String pattern;
  final TextStyle style;
}

class TextPartStyleDefinitions {
  TextPartStyleDefinitions({required this.definitionList});

  final List<TextPartStyleDefinition> definitionList;

  RegExp createCombinedPatternBasedOnStyleMap() {
    final String combinedPatternString = definitionList
        .map<String>(
          (final TextPartStyleDefinition textPartStyleDefinition) =>
              textPartStyleDefinition.pattern,
        )
        .join('|');

    return RegExp(
      combinedPatternString,
      multiLine: true,
      caseSensitive: false,
    );
  }

  TextPartStyleDefinition? getStyleOfTextPart(
    final String textPart,
    final String text,
  ) {
    return List<TextPartStyleDefinition?>.from(definitionList).firstWhere(
      (final TextPartStyleDefinition? styleDefinition) {
        if (styleDefinition == null) {
          return false;
        }

        bool hasMatch = false;

        RegExp(styleDefinition.pattern, caseSensitive: false)
            .allMatches(text)
            .forEach(
          (final RegExpMatch currentMatch) {
            if (hasMatch) {
              return;
            }

            if (currentMatch.group(0) == textPart) {
              hasMatch = true;
            }
          },
        );

        return hasMatch;
      },
      orElse: () => null,
    );
  }
}
