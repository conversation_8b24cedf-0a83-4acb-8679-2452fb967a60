// ignore_for_file: avoid_print
import 'package:collection/collection.dart';

import 'dart:convert';
import 'dart:io';

import 'package:mason/mason.dart';

class Utils {
  static String getValueType({
    required final String modelName,
    required final MapEntry<String, dynamic> entry,
  }) {
    final val = entry.value;
    var type = 'dynamic';
    if (val is String) {
      type = '${val.runtimeType}?';
    }
    if (val is int) {
      type = '${val.runtimeType}?';
    }
    if (val is double) {
      type = '${val.runtimeType}?';
    }
    if (val is bool) {
      type = '${val.runtimeType}?';
    }
    if (val is List) {
      final valueList = val;
      type = 'List<${getValueType(
        modelName: modelName,
        entry: MapEntry(entry.key, valueList.firstOrNull),
      )}>';
    }
    if (val is Map) {
      type =
          '${modelName.replaceAll('Model', '')}${entry.key.pascalCase}Model?';
    }

    return type;
  }

  static String getMapperFileContent() {
    var content = '';
    final separator = Platform.pathSeparator;
    final fullPath =
        '${Directory.current.path}${separator}lib${separator}src$separator'
        'core${separator}utils${separator}mappers.dart';
    try {
      final file = File(fullPath);
      content = file.readAsStringSync();
    } catch (_) {
      print('Error when reading json file: $fullPath');
    }

    return content;
  }

  static Map<String, dynamic> getJsonFileFromPath(
    final String? fileName,
  ) {
    var map = <String, dynamic>{};
    final separator = Platform.pathSeparator;
    final fullPath =
        '${Directory.current.path}${separator}assets${separator}mockup'
        '$separator$fileName.json';
    try {
      final file = File(fullPath);
      map = jsonDecode(file.readAsStringSync());
    } catch (_) {
      print('Error when reading json file: $fullPath');
    }

    return map;
  }
}
